/* DocForge AI - Pure CSS Styles */
/* No compilation required - just link this file */

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

/* CSS Reset */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* Root Variables */
:root {
  /* Colors */
  --primary-blue: #2563eb;
  --primary-blue-dark: #1d4ed8;
  --primary-purple: #10b981;
  --accent-green: #10b981;
  --accent-teal: #14b8a6;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;
  --background: #ffffff;
  --surface: #f8fafc;
  --border: #e2e8f0;

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, #2563eb 0%, #10b981 100%);
  --gradient-text: linear-gradient(90deg, #1E3A8A 0%, #3B82F6 50%, #10B981 100%);
  --gradient-accent: linear-gradient(135deg, #10b981 0%, #3b82f6 100%);

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Base Styles */
body {
  font-family: 'Inter', system-ui, sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--background);
  -webkit-font-smoothing: antialiased;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  line-height: 1.3;
  color: var(--text-primary);
}

/* Base Link Styles */
a {
  color: inherit;
  text-decoration: none !important;
  transition: all 0.2s ease;
}

/* Footer Links - Override inline styles */
footer a {
  text-decoration: none;
}

footer a:hover {
  text-decoration: underline !important;
}

/* List Styles Reset */
ul,
ol {
  list-style: none;
  margin: 0;
  padding: 0;
}

/* Content List Styles - Override reset for content areas */
.prose ul {
  list-style: disc;
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.prose ol {
  list-style: decimal;
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.prose li {
  margin: 0.5rem 0;
}

/* Specific list styles for privacy policy content */
article ul {
  list-style: disc;
  margin: 1rem 0;
  padding-left: 1.5rem;
}

article ol {
  list-style: decimal;
  margin: 1rem 0;
  padding-left: 1.5rem;
}

article li {
  margin: 0.5rem 0;
  line-height: 1.6;
}

/* List Style Utilities */
.list-disc {
  list-style-type: disc;
}

.list-decimal {
  list-style-type: decimal;
}

.list-inside {
  list-style-position: inside;
}

.list-outside {
  list-style-position: outside;
}

/* Layout Utilities */
.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}

.section {
  padding: 5rem 0;
}

.text-center {
  text-align: center;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

/* Flexbox Utilities */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-4 {
  gap: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-8 {
  gap: 2rem;
}

/* Grid */
.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, 1fr);
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-cols-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-cols-4 {
  grid-template-columns: repeat(4, 1fr);
}

/* Grid Column Span */
.col-span-1 {
  grid-column: span 1 / span 1;
}

.col-span-2 {
  grid-column: span 2 / span 2;
}

/* Spacing */
.m-0 {
  margin: 0;
}

.m-1 {
  margin: 0.25rem;
}

.m-2 {
  margin: 0.5rem;
}

.m-3 {
  margin: 0.75rem;
}

.m-4 {
  margin: 1rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.mb-10 {
  margin-bottom: 2.5rem;
}

.mb-12 {
  margin-bottom: 3rem;
}

.mb-16 {
  margin-bottom: 4rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mt-12 {
  margin-top: 3rem;
}

.mt-20 {
  margin-top: 5rem;
}

.ml-1 {
  margin-left: 0.25rem;
}

.ml-4 {
  margin-left: 1rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mr-3 {
  margin-right: 0.75rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-4 {
  padding: 1rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-8 {
  padding: 2rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}

.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}

.pt-2 {
  padding-top: 0.5rem;
}

.pt-16 {
  padding-top: 4rem;
}

.pt-28 {
  padding-top: 7rem;
}

.pt-32 {
  padding-top: 8rem;
}

.pb-3 {
  padding-bottom: 0.75rem;
}

.pb-20 {
  padding-bottom: 5rem;
}

/* Border Utilities */
.border-b {
  border-bottom: 1px solid;
}

/* Typography */
.text-xs {
  font-size: 0.75rem;
}

.text-sm {
  font-size: 0.875rem;
}

.text-base {
  font-size: 1rem;
}

.text-lg {
  font-size: 1.125rem;
}

.text-xl {
  font-size: 1.25rem;
}

.text-2xl {
  font-size: 1.5rem;
}

.text-3xl {
  font-size: 1.875rem;
}

.text-4xl {
  font-size: 2.25rem;
}

.text-5xl {
  font-size: 3rem;
}

.text-6xl {
  font-size: 3.75rem;
}

.text-7xl {
  font-size: 4.5rem;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

.font-black {
  font-weight: 900;
}

.leading-tight {
  line-height: 1.25;
}

.leading-relaxed {
  line-height: 1.625;
}

/* Colors */
.text-white {
  color: #ffffff;
}

.text-gray-200 {
  color: #e5e7eb;
}

.text-gray-500 {
  color: #6b7280;
}

.text-gray-600 {
  color: #4b5563;
}

.text-gray-800 {
  color: #1f2937;
}

.text-gray-900 {
  color: #111827;
}

.text-green-500 {
  color: #10b981;
}

.text-green-600 {
  color: #059669;
}

.text-blue-600 {
  color: #2563eb;
}

.text-yellow-400 {
  color: #facc15;
}

.bg-white {
  background-color: #ffffff;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.bg-gray-100 {
  background-color: #f3f4f6;
}

.bg-gray-800 {
  background-color: #1f2937;
}

.bg-gray-900 {
  background-color: #111827;
}

.bg-green-500 {
  background-color: #10b981;
}

.bg-red-500 {
  background-color: #ef4444;
}

.bg-yellow-500 {
  background-color: #eab308;
}

.bg-white\/80 {
  background-color: rgba(255, 255, 255, 0.8);
}

/* Borders */
.border {
  border: 1px solid var(--border);
}

.border-2 {
  border: 2px solid;
}

.border-t {
  border-top: 1px solid;
}

.border-gray-100 {
  border-color: #f3f4f6;
}

.border-gray-800 {
  border-color: #1f2937;
}

.border-white {
  border-color: #ffffff;
}

.border-white\/20 {
  border-color: rgba(255, 255, 255, 0.2);
}

.rounded {
  border-radius: 0.25rem;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.rounded-2xl {
  border-radius: 1rem;
}

.rounded-full {
  border-radius: 9999px;
}

/* Shadows */
.shadow-sm {
  box-shadow: var(--shadow-sm);
}

.shadow-md {
  box-shadow: var(--shadow-md);
}

.shadow-lg {
  box-shadow: var(--shadow-lg);
}

.shadow-xl {
  box-shadow: var(--shadow-xl);
}

/* Sizing */
.w-1\/2 {
  width: 50%;
}

.w-3\/4 {
  width: 75%;
}

.w-2 {
  width: 0.5rem;
}

.w-3 {
  width: 0.75rem;
}

.w-4 {
  width: 1rem;
}

.w-5 {
  width: 1.25rem;
}

.w-6 {
  width: 1.5rem;
}

.w-8 {
  width: 2rem;
}

.w-10 {
  width: 2.5rem;
}

.w-12 {
  width: 3rem;
}

.w-full {
  width: 100%;
}

.h-2 {
  height: 0.5rem;
}

.h-3 {
  height: 0.75rem;
}

.h-4 {
  height: 1rem;
}

.h-5 {
  height: 1.25rem;
}

.h-6 {
  height: 1.5rem;
}

.h-8 {
  height: 2rem;
}

.h-10 {
  height: 2.5rem;
}

.h-12 {
  height: 3rem;
}

.h-20 {
  height: 5rem;
}

.max-w-3xl {
  max-width: 48rem;
}

.max-w-4xl {
  max-width: 56rem;
}

.max-w-5xl {
  max-width: 64rem;
}

.max-w-7xl {
  max-width: 80rem;
}

/* Positioning */
.top-0 {
  top: 0;
}

.left-0 {
  left: 0;
}

.right-0 {
  right: 0;
}

.inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.z-10 {
  z-index: 10;
}

.z-50 {
  z-index: 50;
}

/* Display */
.block {
  display: block;
}

.inline-flex {
  display: inline-flex;
}

.hidden {
  display: none;
}

/* Text Alignment */
.text-left {
  text-align: left;
}

/* Flex Wrap */
.flex-wrap {
  flex-wrap: wrap;
}

/* Font Style */
.italic {
  font-style: italic;
}

/* Overflow */
.overflow-hidden {
  overflow: hidden;
}

/* Transitions */
.transition-all {
  transition: all 0.3s ease;
}

.transition-colors {
  transition: color 0.2s ease, background-color 0.2s ease;
}

.transition-transform {
  transition: transform 0.3s ease;
}

/* Transforms */
.transform {
  transform: translateZ(0);
}

.-translate-x-1\/2 {
  transform: translateX(-50%);
}

.hover\:scale-110:hover {
  transform: scale(1.1);
}

.hover\:-translate-y-1:hover {
  transform: translateY(-0.25rem);
}

.hover\:-translate-y-2:hover {
  transform: translateY(-0.5rem);
}

/* Animations */
@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Backdrop Effects */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

.backdrop-blur-xl {
  backdrop-filter: blur(24px);
}

/* Opacity */
.opacity-30 {
  opacity: 0.3;
}

/* Duration Classes */
.duration-200 {
  transition-duration: 200ms;
}

.duration-300 {
  transition-duration: 300ms;
}

/* Space Between */
.space-x-3> :not([hidden])~ :not([hidden]) {
  margin-right: 0;
  margin-left: 0.75rem;
}

.space-x-4> :not([hidden])~ :not([hidden]) {
  margin-right: 0;
  margin-left: 1rem;
}

.space-x-8> :not([hidden])~ :not([hidden]) {
  margin-right: 0;
  margin-left: 2rem;
}

.space-y-1> :not([hidden])~ :not([hidden]) {
  margin-top: 0.25rem;
  margin-bottom: 0;
}

.space-y-2> :not([hidden])~ :not([hidden]) {
  margin-top: 0.5rem;
  margin-bottom: 0;
}

.space-y-3> :not([hidden])~ :not([hidden]) {
  margin-top: 0.75rem;
  margin-bottom: 0;
}

.space-y-4> :not([hidden])~ :not([hidden]) {
  margin-top: 1rem;
  margin-bottom: 0;
}

/* Group Hover Effects */
.group:hover .group-hover\:scale-110 {
  transform: scale(1.1);
}

/* Hover Effects */
.hover\:bg-gray-50:hover {
  background-color: #f9fafb;
}

.hover\:bg-gray-100:hover {
  background-color: #f3f4f6;
}

.hover\:text-blue-400:hover {
  color: #60a5fa;
}

.hover\:text-blue-600:hover {
  color: #2563eb;
}

/* Component Styles */

/* Navigation */
.nav-glass {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(24px);
  border-bottom: 1px solid var(--border);
  box-shadow: var(--shadow-sm);
}

/* Buttons */
.btn-primary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  background: var(--gradient-primary);
  color: white;
  font-weight: 600;
  border-radius: 0.75rem;
  border: none;
  cursor: pointer;
  text-decoration: none;
  box-shadow: var(--shadow-lg);
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.btn-secondary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  background: transparent;
  color: var(--primary-blue);
  font-weight: 600;
  border: 2px solid var(--primary-blue);
  border-radius: 0.75rem;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: var(--primary-blue);
  color: white;
  transform: translateY(-2px);
}

/* Cards */
.card {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  border: 1px solid var(--border);
  box-shadow: var(--shadow-lg);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

/* Glass Card */
.glass-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(24px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1rem;
  box-shadow: var(--shadow-xl);
}

/* Text Gradient - FIXED */
.text-gradient {
  background: var(--gradient-text);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

/* Icon Backgrounds */
.icon-bg-blue {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.25);
}

.icon-bg-teal {
  background: linear-gradient(135deg, #14b8a6 0%, #0f766e 100%);
  box-shadow: 0 4px 14px 0 rgba(20, 184, 166, 0.25);
}

.icon-bg-orange {
  background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
  box-shadow: 0 4px 14px 0 rgba(249, 115, 22, 0.25);
}

.icon-bg-green {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  box-shadow: 0 4px 14px 0 rgba(34, 197, 94, 0.25);
}

.icon-bg-purple {
  background: linear-gradient(135deg, #a855f7 0%, #7c3aed 100%);
  box-shadow: 0 4px 14px 0 rgba(168, 85, 247, 0.25);
}

/* Section Backgrounds */
.section-bg-primary {
  background: var(--gradient-primary);
}

.bg-gradient-muted {
  background: linear-gradient(135deg, #e5e7eb 0%, #f3f4f6 100%);
}

/* Background Patterns */
.bg-mesh {
  background:
    radial-gradient(at 40% 20%, rgba(59, 130, 246, 0.1) 0px, transparent 50%),
    radial-gradient(at 80% 0%, rgba(168, 85, 247, 0.1) 0px, transparent 50%),
    radial-gradient(at 0% 50%, rgba(16, 185, 129, 0.1) 0px, transparent 50%);
}

.pattern-dots {
  background-image: radial-gradient(circle, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Animations */
.fade-in {
  opacity: 0;
  transform: translateY(2rem);
  transition: all 0.7s ease-out;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

.hover-lift {
  transition: all 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

/* Mobile-First Responsive Design */

/* Mobile optimizations (up to 640px) */
@media (max-width: 639px) {

  /* Larger touch targets for mobile */
  .btn-primary,
  .btn-secondary {
    min-height: 48px;
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }

  /* Better form field sizing for mobile */
  .form-input,
  select,
  textarea {
    min-height: 48px;
    font-size: 16px;
    /* Prevents zoom on iOS */
    padding: 0.875rem 1rem;
  }



  /* Better spacing for mobile forms */
  .space-y-6> :not([hidden])~ :not([hidden]) {
    margin-top: 2rem;
  }

  /* Mobile hero text sizing */
  .text-4xl {
    font-size: 2rem;
    line-height: 1.1;
  }
  
  .text-5xl {
    font-size: 2.5rem;
    line-height: 1.1;
  }

  /* Mobile content spacing */
  .prose h2 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }

  .prose h3 {
    font-size: 1.25rem;
    margin-bottom: 0.75rem;
  }

  .prose p {
    font-size: 0.875rem;
    line-height: 1.6;
    margin-bottom: 1rem;
  }

  .prose ul {
    font-size: 0.875rem;
    margin-left: 0.5rem;
  }

  /* Mobile article spacing */
  article {
    margin-bottom: 2rem;
  }

  /* Mobile section spacing */
  section {
    margin-bottom: 1.5rem;
  }

  /* Mobile card padding */
  .card {
    padding: 1.5rem;
  }

  /* Mobile glass card padding */
  .glass-card {
    padding: 1.5rem;
  }

  /* Mobile navigation improvements */
  .nav-glass {
    backdrop-filter: blur(20px);
  }

  /* Mobile hero section padding fix - ensure content is not cut off */
  .pt-28 {
    padding-top: 8.5rem !important; /* 136px - extra clearance for mobile browsers */
  }

  /* Additional mobile viewport fixes */
  body {
    padding-top: 0; /* Ensure no additional body padding */
  }

  /* Ensure fixed nav doesn't interfere with content */
  main {
    position: relative;
    z-index: 1;
  }

  /* Mobile menu improvements */
  #mobile-menu {
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.98);
  }

  /* Mobile form button improvements */
  .flex-col .btn-primary,
  .flex-col .btn-secondary {
    width: 100%;
    justify-content: center;
  }

  /* Mobile form layout improvements */
  .grid.grid-cols-1.md\\:grid-cols-2 {
    gap: 1.5rem;
  }

  .grid.grid-cols-1.md\\:grid-cols-3 {
    gap: 1rem;
  }

  /* Mobile content max-width for better readability */
  .prose {
    max-width: 100%;
  }

  .prose p,
  .prose li {
    max-width: 100%; /* Full width on mobile for better use of space */
    hyphens: auto; /* Enable hyphenation for better text flow */
    word-wrap: break-word;
  }

  /* Mobile list improvements */
  .prose ul,
  article ul {
    padding-left: 1.25rem; /* Slightly less padding on mobile */
  }

  .list-disc {
    list-style-type: disc !important; /* Ensure bullets show on mobile */
  }

  /* Improve mobile text spacing */
  .prose p + p {
    margin-top: 1rem;
  }

  .prose ul + p,
  .prose p + ul {
    margin-top: 1rem;
  }

  /* Mobile navigation improvements */
  .nav-glass {
    padding: 0.5rem 0;
  }

  /* Mobile menu backdrop */
  #mobile-menu {
    position: fixed;
    top: 80px;
    left: 0;
    right: 0;
    z-index: 40;
    max-height: calc(100vh - 80px);
    overflow-y: auto;
    -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
  }

  /* Prevent horizontal scroll on mobile */
  body {
    overflow-x: hidden;
  }

  /* Improve mobile text selection */
  * {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  }

  /* Focus improvements for mobile */
  button:focus,
  a:focus {
    outline: 2px solid #2563eb;
    outline-offset: 2px;
  }

  /* Improve mobile scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Better mobile typography */
  h1, h2, h3, h4, h5, h6 {
    word-wrap: break-word;
    hyphens: auto;
  }

  /* Mobile textarea improvements */
  textarea {
    min-height: 120px;
    resize: vertical;
  }

  /* Mobile error message improvements */
  [role="alert"] {
    font-size: 0.875rem;
    line-height: 1.4;
    margin-top: 0.5rem;
  }

  /* Mobile success/error message improvements */
  #form-success,
  #form-error {
    padding: 1rem;
    border-radius: 0.5rem;
    margin: 1rem 0;
    font-size: 0.875rem;
    line-height: 1.5;
  }
}

/* Small screens and up (640px+) */
@media (min-width: 640px) {
  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:text-base {
    font-size: 1rem;
  }

  .sm\:text-lg {
    font-size: 1.125rem;
  }

  .sm\:text-5xl {
    font-size: 3rem;
  }

  .sm\:mb-8 {
    margin-bottom: 2rem;
  }

  .sm\:mb-16 {
    margin-bottom: 4rem;
  }

  .sm\:pt-32 {
    padding-top: 8rem;
  }

  .sm\:pb-20 {
    padding-bottom: 5rem;
  }

  .sm\:py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, 1fr);
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, 1fr);
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, 1fr);
  }

  .md\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .md\:flex {
    display: flex !important;
  }

  .md\:hidden {
    display: none !important;
  }

  .md\:text-2xl {
    font-size: 1.5rem;
  }

  .md\:text-5xl {
    font-size: 3rem;
  }

  .md\:text-6xl {
    font-size: 3.75rem;
  }

  .md\:text-xl {
    font-size: 1.25rem;
  }

  /* Ensure mobile menu button is hidden on desktop */
  #mobile-menu-btn {
    display: none !important;
  }

  .md\:py-20 {
    padding-top: 5rem;
    padding-bottom: 5rem;
  }

  /* Tablet-specific improvements */
  .prose {
    font-size: 1rem;
    line-height: 1.7;
  }

  .prose h2 {
    font-size: 1.875rem;
  }

  .prose h3 {
    font-size: 1.5rem;
  }

  /* Better line lengths for tablet */
  .prose p,
  .prose li {
    max-width: 70ch;
  }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, 1fr);
  }

  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\:text-7xl {
    font-size: 4.5rem;
  }

  /* Desktop-specific improvements */
  .prose p,
  .prose li {
    max-width: 65ch; /* Optimal reading line length for desktop */
  }

  .prose {
    font-size: 1.125rem;
    line-height: 1.8;
  }
}

/* Specific Component Fixes */
.pricing-card {
  position: relative;
}

.pricing-card .badge {
  position: absolute;
  top: -1rem;
  left: 50%;
  transform: translateX(-50%);
  background: var(--primary-blue-dark);
  color: white;
  padding: 0.25rem 1rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 600;
}

/* Pricing Button Text Size */
.card .btn-primary,
.card .btn-secondary {
  font-size: 1rem;
  font-weight: 600;
}

/* Mobile Menu */
.mobile-menu {
  background: white;
  box-shadow: var(--shadow-lg);
  padding: 1rem;
  border-radius: 0.5rem;
  margin-top: 0.5rem;
}

.mobile-menu a {
  display: block;
  padding: 0.75rem 1rem;
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
}

.mobile-menu a:hover {
  background: var(--surface);
  color: var(--primary-blue);
}

/* Form Styles */
.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border);
  border-radius: 0.5rem;
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(4px);
  transition: all 0.2s ease;
  font-size: 1rem;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-input.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-input.success {
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* Enhanced form field validation states */
input.error,
select.error,
textarea.error {
  border-color: #ef4444 !important;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
  background-color: rgba(254, 242, 242, 0.5);
}

input.success,
select.success,
textarea.success {
  border-color: #10b981 !important;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1) !important;
  background-color: rgba(240, 253, 244, 0.5);
}

/* Loading spinner animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Form validation error messages */
[role="alert"] {
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: block;
}

/* Enhanced radio button validation states */
input[type="radio"]:invalid {
  border-color: #ef4444;
}

/* Form submission states */
.btn-primary:disabled {
  opacity: 0.75;
  cursor: not-allowed;
}

.btn-primary:disabled:hover {
  transform: none;
}

/* Character counter styling */
#message-count {
  font-size: 0.75rem;
  transition: color 0.2s ease;
}

/* Success message animation */
#form-success {
  transition: all 0.3s ease;
}

#form-error {
  transition: all 0.3s ease;
}

/* Focus Ring Utilities */
.focus\:ring-2:focus {
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
}

.focus\:ring-blue-500:focus {
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
}

.focus\:border-blue-500:focus {
  border-color: #3b82f6;
}

/* Resize Utilities */
.resize-vertical {
  resize: vertical;
}

/* Background Opacity */
.bg-white\/50 {
  background-color: rgba(255, 255, 255, 0.5);
}

.bg-white\/80 {
  background-color: rgba(255, 255, 255, 0.8);
}

.bg-green-50 {
  background-color: #f0fdf4;
}

.bg-red-50 {
  background-color: #fef2f2;
}

/* Border Colors */
.border-green-200 {
  border-color: #bbf7d0;
}

.border-red-200 {
  border-color: #fecaca;
}

.border-gray-300 {
  border-color: #d1d5db;
}

/* Text Colors */
.text-red-500 {
  color: #ef4444;
}

.text-green-500 {
  color: #10b981;
}

.text-green-700 {
  color: #047857;
}

.text-green-800 {
  color: #065f46;
}

.text-red-700 {
  color: #b91c1c;
}

.text-red-800 {
  color: #991b1b;
}

/* Additional Utility Classes */
.opacity-75 {
  opacity: 0.75;
}

.flex-1 {
  flex: 1 1 0%;
}

 
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
  pointer-events: none;
}

.pattern-dots {
  background-image: radial-gradient(circle, rgba(255, 255, 255, 0.15) 1px, transparent 1px);
  background-size: 20px 20px;
  background-position: 0 0, 10px 10px;
}

/* Text Gradient */
.text-gradient {
  background: var(--gradient-text);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}



/* Touch target utility class */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0.75rem 1rem;
}

/* Mobile-first navigation improvements */
.nav-glass {
  transition: all 0.3s ease;
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
}

/* Ensure proper font rendering on mobile */
@media (max-width: 767px) {
  body {
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-size-adjust: 100%;
  }
}

/* Improved mobile menu positioning */
@media (max-width: 767px) {
  #mobile-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  }

  /* Ensure mobile menu items have proper spacing */
  #mobile-menu a {
    font-size: 1rem;
    line-height: 1.5;
    min-height: 48px;
    display: flex;
    align-items: center;
  }

  /* Mobile navigation improvements */
  nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 50;
  }

  /* Adjust main content for fixed nav */
  main {
    position: relative;
    z-index: 1;
  }
}

/* Mobile touch improvements */
@media (max-width: 767px) {

  /* Improve tap targets */
  a,
  button,
  input,
  select,
  textarea,
  label {
    -webkit-tap-highlight-color: rgba(59, 130, 246, 0.1);
  }

  /* Prevent text selection on buttons */
  .btn-primary,
  .btn-secondary {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
  }

  /* Better scrolling on mobile */
  body {
    -webkit-overflow-scrolling: touch;
  }

  /* Improve form field appearance on mobile */
  input:not([type="radio"]):not([type="checkbox"]),
  select,
  textarea {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border-radius: 0.5rem;
  }

  /* Mobile-specific select styling */
  select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
  }

  /* Mobile hero section improvements */
  .bg-mesh {
    background-attachment: scroll;
    /* Better performance on mobile */
  }

  /* Mobile card hover effects - reduce for better performance */
  .card:hover,
  .hover-lift:hover {
    transform: translateY(-2px);
  }

  /* Mobile glass card performance */
  .glass-card {
    backdrop-filter: blur(16px);
    /* Reduce blur for better performance */
  }
}

/* Fieldset and Legend Reset */
fieldset {
  border: none;
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

/* Radio Button Styling */
input[type="radio"] {
  appearance: none;
  width: 1rem;
  height: 1rem;
  border: 2px solid #d1d5db;
  border-radius: 50%;
  background: white;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

input[type="radio"]:checked {
  border-color: #3b82f6;
  background: #3b82f6;
}

input[type="radio"]:checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 0.375rem;
  height: 0.375rem;
  border-radius: 50%;
  background: white;
}

input[type="radio"]:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* Mobile radio button improvements */
@media (max-width: 767px) {

  /* Override utility classes with more specific selector - copy ALL desktop styles */
  label input[type="radio"] {
    appearance: none !important;
    width: 1rem !important;
    height: 1rem !important;
    border: 2px solid #d1d5db !important;
    border-radius: 50% !important;
    background: white !important;
    position: relative !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    flex-shrink: 0 !important;
    min-width: 1rem !important;
    min-height: 1rem !important;
    max-width: 1rem !important;
    max-height: 1rem !important;
  }

  label input[type="radio"]:checked {
    border-color: #3b82f6 !important;
    background: #3b82f6 !important;
  }

  label input[type="radio"]:focus {
    outline: none !important;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
  }

  label input[type="radio"]:checked::after {
    width: 0.375rem;
    height: 0.375rem;
  }

  /* Larger touch targets for radio button labels - but don't affect radio button size */
  label:has(input[type="radio"]) {
    min-height: 48px;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    cursor: pointer;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  }

  /* Better spacing for radio button content */
  label:has(input[type="radio"]) .ml-4 {
    margin-left: 1rem;
    flex: 1;
  }
}

/* Table of Contents Styles */
.toc-link {
  position: relative;
  transition: all 0.2s ease;
}

.toc-link:hover {
  background-color: rgba(59, 130, 246, 0.1);
  color: #2563eb;
}

.toc-link.active {
  background-color: rgba(59, 130, 246, 0.15);
  color: #1d4ed8;
  font-weight: 500;
  border-left: 3px solid #2563eb;
  padding-left: 12px;
}

.toc-link.active::before {
  content: '';
  position: absolute;
  left: -3px;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(135deg, #2563eb 0%, #10b981 100%);
  border-radius: 0 2px 2px 0;
}

/* Mobile TOC Styles */
@media (max-width: 1023px) {
  #toc-nav {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 2rem;
  }
  
  .toc-link {
    flex: 1;
    min-width: calc(50% - 4px);
    text-align: center;
    padding: 10px 12px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background: white;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }
  
  .toc-link:hover {
    border-color: #3b82f6;
    background-color: rgba(59, 130, 246, 0.05);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .toc-link.active {
    border-color: #2563eb;
    background-color: rgba(59, 130, 246, 0.1);
    color: #1d4ed8;
    border-left: 1px solid #2563eb;
    padding-left: 12px;
    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.2);
  }
  
  .toc-link.active::before {
    display: none;
  }
  
  /* Make TOC container more prominent on mobile */
  aside {
    order: -1; /* Show TOC before content on mobile */
    margin-bottom: 2rem;
  }
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Terms section spacing */
.terms-section {
  scroll-margin-top: 120px; /* Account for fixed nav */
}

/* Responsive adjustments for sticky TOC */
@media (min-width: 1024px) {
  .lg\:sticky {
    position: sticky;
    top: 6rem; /* 24 in Tailwind = 6rem */
  }
  
  .lg\:self-start {
    align-self: flex-start;
  }
}

/* Enhanced mobile responsiveness */
@media (max-width: 640px) {
  .toc-link {
    min-width: 100%;
    font-size: 0.8rem;
    padding: 6px 10px;
  }
}/* Scr
een reader only class for accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus styles for better accessibility */
.toc-link:focus {
  outline: none;
  box-shadow: 0 0 0 2px #2563eb, 0 0 0 4px rgba(37, 99, 235, 0.2);
  border-radius: 4px;
}

/* Ensure focus is visible on mobile too */
@media (max-width: 1023px) {
  .toc-link:focus {
    box-shadow: 0 0 0 2px #2563eb, 0 0 0 4px rgba(37, 99, 235, 0.2);
    transform: none; /* Don't apply hover transform on focus for better accessibility */
  }
}