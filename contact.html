<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Contact Us | DocForge AI</title>
  <link rel="stylesheet" href="css/styles.css" />
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
</head>
<body class="bg-white text-primary min-h-screen flex flex-col">
  <nav class="fixed top-0 left-0 right-0 nav-glass z-50 transition-all duration-300">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-20">
        <div class="flex items-center">
          <div class="flex items-center">
            <svg class="w-10 h-10 mr-3" viewBox="0 0 32 32" fill="currentColor" style="color: #1E3A8A;"><circle cx="16" cy="16" r="16"/></svg>
            <span class="text-2xl font-bold text-gray-900">DocForge AI</span>
          </div>
        </div>
        <div class="hidden md:flex items-center space-x-8">
          <a href="index.html" class="text-gray-600 transition-colors font-medium" style="color: #6B7280;">Home</a>
          <a href="#features" class="text-gray-600 transition-colors font-medium" style="color: #6B7280;">Features</a>
          <a href="#pricing" class="text-gray-600 transition-colors font-medium" style="color: #6B7280;">Pricing</a>
          <a href="#" class="btn-primary">Contact Us</a>
        </div>
      </div>
    </div>
  </nav>
  <main class="flex-1 flex items-center justify-center pt-32 pb-12 bg-surface">
    <div class="w-full max-w-2xl bg-white rounded-xl shadow-lg p-8">
      <h1 class="text-3xl font-bold mb-4 text-primary">Contact Us</h1>
      <p class="text-text-secondary mb-8">We'd love to hear from you! Fill out the form below and our team will get back to you soon.</p>
      <form class="space-y-6">
        <div>
          <label for="name" class="block text-sm font-medium text-text-primary">Name</label>
          <input type="text" id="name" name="name" class="mt-1 block w-full rounded-md border border-border shadow-sm p-3 focus:border-primary-blue focus:ring focus:ring-primary-blue/20" placeholder="John Doe" value="Jane Doe" />
        </div>
        <div>
          <label for="email" class="block text-sm font-medium text-text-primary">Email</label>
          <input type="email" id="email" name="email" class="mt-1 block w-full rounded-md border border-border shadow-sm p-3 focus:border-primary-blue focus:ring focus:ring-primary-blue/20" placeholder="<EMAIL>" value="<EMAIL>" />
        </div>
        <div>
          <label for="message" class="block text-sm font-medium text-text-primary">Message</label>
          <textarea id="message" name="message" rows="4" class="mt-1 block w-full rounded-md border border-border shadow-sm p-3 focus:border-primary-blue focus:ring focus:ring-primary-blue/20" placeholder="How can we help you?">I would like to know more about your services.</textarea>
        </div>
        <button type="submit" class="btn-primary w-full py-3 text-lg">Send Message</button>
      </form>
      <div class="mt-8 text-center text-text-muted">
        <p>Email: <EMAIL></p>
        <p>Phone: ****** 567 8900</p>
        <p>Address: 1234 AI Street, Innovation City, 56789</p>
      </div>
    </div>
  </main>
  <!-- Footer -->
  <footer class="bg-gray-900 text-white pt-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
              <!-- Logo and Description -->
              <div class="col-span-1 md:col-span-2">
                  <div class="flex items-center mb-4">
                      <svg class="w-8 h-8 mr-3" viewBox="0 0 32 32" fill="currentColor" style="color: #3B82F6;">
                          <path
                              d="M8 4h16a4 4 0 0 1 4 4v16a4 4 0 0 1-4 4H8a4 4 0 0 1-4-4V8a4 4 0 0 1 4-4zm2 6v12h12V10H10zm2 2h8v2h-8v-2zm0 4h8v2h-8v-2zm0 4h6v2h-6v-2z" />
                      </svg>
                      <span class="text-xl font-bold">DocForge AI</span>
                  </div>
                  <p class="text-white mb-6 leading-relaxed">
                      Transform your ideas into professional documents with the power of AI. Create and share
                      beautiful documents 10x faster.
                  </p>
                  <div class="flex space-x-4">
                      <a href="#" class="text-white transition-all duration-200"
                          onmouseover="this.style.textDecoration='underline'"
                          onmouseout="this.style.textDecoration='none'">
                          <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                              <path
                                  d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z" />
                          </svg>
                      </a>
                      <a href="#" class="text-white transition-all duration-200"
                          onmouseover="this.style.textDecoration='underline'"
                          onmouseout="this.style.textDecoration='none'">
                          <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                              <path
                                  d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
                          </svg>
                      </a>
                  </div>
              </div>

              <!-- Quick Links -->
              <div>
                  <h3 class="text-lg font-semibold mb-4 text-white">Product</h3>
                  <ul class="space-y-2">
                      <li><a href="#" class="text-white transition-all duration-200"
                              onmouseover="this.style.textDecoration='underline'"
                              onmouseout="this.style.textDecoration='none'">Features</a></li>
                      <li><a href="#" class="text-white transition-all duration-200"
                              onmouseover="this.style.textDecoration='underline'"
                              onmouseout="this.style.textDecoration='none'">Pricing</a></li>
                  </ul>
              </div>

              <!-- Support -->
              <div>
                  <h3 class="text-lg font-semibold mb-4 text-white">Support</h3>
                  <ul class="space-y-2">
                      <li><a href="#" class="text-white transition-all duration-200"
                              onmouseover="this.style.textDecoration='underline'"
                              onmouseout="this.style.textDecoration='none'">Help Center</a></li>
                      <li><a href="#" class="text-white transition-all duration-200"
                              onmouseover="this.style.textDecoration='underline'"
                              onmouseout="this.style.textDecoration='none'">Contact Us</a></li>
                      <li><a href="#" class="text-white transition-all duration-200"
                              onmouseover="this.style.textDecoration='underline'"
                              onmouseout="this.style.textDecoration='none'">Privacy Policy</a></li>
                      <li><a href="#" class="text-white transition-all duration-200"
                              onmouseover="this.style.textDecoration='underline'"
                              onmouseout="this.style.textDecoration='none'">Terms of Service</a></li>
                  </ul>
              </div>
          </div>

          <div class="border-t border-gray-800 mt-12 py-6 text-center text-gray-200 text-sm">
              <p>&copy; 2024 DocForge AI. All rights reserved.</p>
          </div>
      </div>
  </footer>
</body>
</html>
