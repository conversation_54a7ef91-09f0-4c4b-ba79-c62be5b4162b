# DocForge AI

Transform ideas into professional documents 10x faster with DocForge AI. Advanced AI-powered document creation for professionals, students, and researchers.

## 🚀 Features

- **AI-Powered Writing**: Generate high-quality content with advanced AI
- **Smart Templates**: Choose from hundreds of professionally designed templates
- **Advanced Analytics**: Track document performance and engagement metrics
- **Enterprise Security**: Bank-level encryption and compliance standards
- **Export Anywhere**: Export to PDF, Word, Google Docs, or publish directly

## 🛠️ Tech Stack

- **Pure CSS**: No build process required, just edit and refresh
- **Vanilla JavaScript**: Lightweight and fast
- **Responsive Design**: Works perfectly on all devices
- **Modern Gradients**: Beautiful gradient system for visual appeal

## 📁 Project Structure

```
docforge-ai/
├── index.html          # Main landing page
├── css/
│   └── styles.css      # Pure CSS styles (no compilation needed)
├── js/                 # JavaScript files
├── public/             # Static assets
└── README.md           # This file
```

## 🚀 Getting Started

### Option 1: Simple File Server
Just open `index.html` in your browser or use any local server:

```bash
# Python 3
python -m http.server 8000

# Python 2
python -m SimpleHTTPServer 8000

# Node.js (if you have http-server installed)
npx http-server
```

### Option 2: Using npm scripts
```bash
npm run start    # Starts Python server on port 8000
npm run serve    # Alternative command
```

Then visit `http://localhost:8000`

## 🎨 Styling

The project uses **pure CSS** with no build process required:

- **No compilation needed** - just edit `css/styles.css` and refresh
- **Custom CSS variables** for consistent theming
- **Responsive design** with mobile-first approach
- **Smooth animations** and hover effects
- **Gradient system** for modern visual appeal

### Key CSS Classes

- `.btn-primary` - Primary gradient buttons
- `.btn-secondary` - Secondary outline buttons
- `.text-gradient` - Gradient text effects
- `.card` - Card components with hover effects
- `.icon-bg-*` - Gradient icon backgrounds
- `.section-bg-primary` - Section gradient backgrounds

## 🔧 Customization

### Colors
Edit the CSS variables in `css/styles.css`:

```css
:root {
  --primary-blue: #2563eb;
  --primary-purple: #10b981;
  --accent-green: #10b981;
  --gradient-primary: linear-gradient(135deg, #2563eb 0%, #10b981 100%);
}
```

### Typography
The project uses:
- **Inter** for body text
- **Poppins** for headings

## 📱 Responsive Design

The site is fully responsive with breakpoints:
- Mobile: < 640px
- Tablet: 640px - 1024px
- Desktop: > 1024px

## 🌟 Performance

- **No build process** - instant development
- **Pure CSS** - no framework overhead
- **Optimized images** and assets
- **Minimal JavaScript** for fast loading

## 📄 License

MIT License - feel free to use this project for your own purposes.

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

---

**Built with ❤️ using pure CSS and vanilla JavaScript**