// Blog JavaScript functionality
let blogData = null;
let filteredPosts = [];
let currentCategory = 'all';

function domReady(callback) {
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', callback);
    } else {
        callback();
    }
}

domReady(function () {
    console.log('Blog JavaScript loaded');
    initMobileMenu();

    if (window.location.pathname.includes('/blog/') && !window.location.pathname.includes('/blog/posts/')) {
        console.log('Initializing blog listing page');
        initBlog();
    }
});

async function initBlog() {
    try {
        console.log('Starting blog initialization...');
        showLoadingState();
        await loadBlogData();
        initCategoryFilters();
        renderPosts();
        hideLoadingState();
        console.log('Blog initialization complete');
    } catch (error) {
        console.error('Error initializing blog:', error);
        showError('Failed to load blog posts. Please try again later.');
    }
}

async function loadBlogData() {
    try {
        console.log('Loading blog data...');
        const response = await fetch('data/posts.json');
        if (!response.ok) {
            throw new Error('HTTP error! status: ' + response.status);
        }
        blogData = await response.json();
        filteredPosts = [...blogData.posts];
        console.log('Blog data loaded:', blogData.posts.length, 'posts');
    } catch (error) {
        console.error('Error loading blog data:', error);
        throw error;
    }
}

function initCategoryFilters() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    filterButtons.forEach(button => {
        button.addEventListener('click', handleCategoryFilter);
    });
}

function handleCategoryFilter(event) {
    const button = event.target;
    const category = button.dataset.category;
    document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
    button.classList.add('active');
    currentCategory = category;
    filterAndRenderPosts();
}

function filterAndRenderPosts() {
    if (!blogData || !blogData.posts) {
        console.error('No blog data available');
        return;
    }
    let posts = [...blogData.posts];
    if (currentCategory !== 'all') {
        posts = posts.filter(post => post.category === currentCategory);
    }
    filteredPosts = posts;
    renderPosts();
}

function renderPosts() {
    console.log('Rendering posts...');
    const blogGrid = document.getElementById('blog-grid');
    if (!blogGrid) {
        console.error('Blog grid element not found');
        return;
    }
    if (filteredPosts.length === 0) {
        blogGrid.innerHTML = '<div class="text-center py-16"><p class="text-gray-600">No articles found.</p></div>';
        return;
    }
    const sortedPosts = filteredPosts.sort((a, b) => new Date(b.publishDate) - new Date(a.publishDate));
    const postsHTML = sortedPosts.map(post => createPostCard(post)).join('');
    blogGrid.innerHTML = postsHTML;
    addPostCardHandlers();
    console.log('Posts rendered successfully');
}

function createPostCard(post) {
    const categories = blogData.categories || [];
    const category = categories.find(cat => cat.id === post.category);
    const categoryName = category ? category.name : post.category;
    const categoryColor = category ? category.color : '#6b7280';

    return '<article class="blog-card" data-post-id="' + post.id + '">' +
        '<div class="blog-card-image-container">' +
        '<img src="' + getPlaceholderImage() + '" alt="' + escapeHtml(post.image.alt) + '" class="blog-card-image">' +
        '</div>' +
        '<div class="blog-card-content">' +
        '<div class="blog-card-meta">' +
        '<span class="blog-card-category" style="background: ' + categoryColor + '">' + escapeHtml(categoryName) + '</span>' +
        '<time class="blog-card-date" datetime="' + post.publishDate + '">' + formatDate(post.publishDate) + '</time>' +
        '<span class="reading-time">' + post.readingTime + ' min read</span>' +
        '</div>' +
        '<h2 class="blog-card-title">' + escapeHtml(post.title) + '</h2>' +
        '<p class="blog-card-excerpt">' + escapeHtml(post.excerpt) + '</p>' +
        '<div class="blog-card-footer">' +
        '<div class="blog-card-author"><span>' + escapeHtml(post.author.name) + '</span></div>' +
        '<div class="blog-card-tags">' + post.tags.slice(0, 2).map(tag => '<span class="tag">' + escapeHtml(tag) + '</span>').join('') + '</div>' +
        '</div>' +
        '</div>' +
        '</article>';
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function addPostCardHandlers() {
    const postCards = document.querySelectorAll('.blog-card');
    postCards.forEach(card => {
        card.addEventListener('click', function () {
            const postId = this.dataset.postId;
            window.location.href = 'posts/' + postId + '/';
        });
    });
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

function getPlaceholderImage() {
    return 'data:image/svg+xml;base64,' + btoa('<svg width="400" height="200" xmlns="http://www.w3.org/2000/svg"><defs><linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.8" /><stop offset="100%" style="stop-color:#10b981;stop-opacity:0.8" /></linearGradient></defs><rect width="100%" height="100%" fill="url(#grad)"/><text x="50%" y="50%" font-family="Inter, sans-serif" font-size="16" font-weight="600" fill="white" text-anchor="middle" dy="0.3em">DocForge AI</text></svg>');
}

function showLoadingState() {
    const loadingState = document.getElementById('loading-state');
    if (loadingState) {
        loadingState.style.display = 'block';
    }
}

function hideLoadingState() {
    const loadingState = document.getElementById('loading-state');
    if (loadingState) {
        loadingState.style.display = 'none';
    }
}

function showError(message) {
    const blogGrid = document.getElementById('blog-grid');
    const loadingState = document.getElementById('loading-state');
    if (loadingState) loadingState.style.display = 'none';
    if (blogGrid) {
        blogGrid.innerHTML = '<div class="text-center py-16"><div class="max-w-md mx-auto"><svg class="w-16 h-16 mx-auto text-red-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" /></svg><h3 class="text-xl font-semibold text-gray-900 mb-2">Oops! Something went wrong</h3><p class="text-gray-600 mb-6">' + message + '</p><button class="btn-primary" onclick="location.reload()">Try Again</button></div></div>';
    }
}

function initMobileMenu() {
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileMenu = document.getElementById('mobile-menu');
    if (mobileMenuBtn && mobileMenu) {
        mobileMenuBtn.addEventListener('click', function () {
            mobileMenu.classList.toggle('hidden');
        });
        document.addEventListener('click', function (event) {
            if (!mobileMenuBtn.contains(event.target) && !mobileMenu.contains(event.target)) {
                mobileMenu.classList.add('hidden');
            }
        });
    }
}

function clearSearch() {
    currentCategory = 'all';
    document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
    const allButton = document.querySelector('.filter-btn[data-category="all"]');
    if (allButton) {
        allButton.classList.add('active');
    }
    filterAndRenderPosts();
}

window.clearSearch = clearSearch;