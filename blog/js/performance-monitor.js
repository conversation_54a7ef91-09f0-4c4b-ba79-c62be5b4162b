// Simple performance monitoring for blog pages

// Monitor page load performance
if ('performance' in window) {
    window.addEventListener('load', function () {
        setTimeout(function () {
            const perfData = performance.getEntriesByType('navigation')[0];
            if (perfData) {
                console.log('Page Load Performance:', {
                    'DOM Content Loaded': Math.round(perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart),
                    'Load Complete': Math.round(perfData.loadEventEnd - perfData.loadEventStart),
                    'Total Load Time': Math.round(perfData.loadEventEnd - perfData.fetchStart)
                });
            }
        }, 0);
    });
}

// Monitor Core Web Vitals if supported
if ('PerformanceObserver' in window) {
    try {
        // Largest Contentful Paint
        const lcpObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const lastEntry = entries[entries.length - 1];
            console.log('LCP:', Math.round(lastEntry.startTime));
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

        // First Input Delay
        const fidObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            entries.forEach((entry) => {
                console.log('FID:', Math.round(entry.processingStart - entry.startTime));
            });
        });
        fidObserver.observe({ entryTypes: ['first-input'] });

    } catch (e) {
        console.warn('Performance monitoring not fully supported:', e);
    }
}