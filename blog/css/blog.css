/* Blog-specific CSS that extends the main styles.css */

/* Blog Grid Layout */
.blog-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

@media (max-width: 768px) {
  .blog-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

/* Blog Card Components */
.blog-card {
  background: white;
  border-radius: 1rem;
  padding: 0;
  border: 1px solid var(--border);
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
  cursor: pointer;
}

/* Performance Optimizations - Image Loading States */
.blog-card-image {
  transition: opacity 0.3s ease, filter 0.3s ease, transform 0.3s ease;
  will-change: opacity, filter;
}

.blog-card-image.lazy-loading {
  filter: blur(5px);
  opacity: 0.7;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.blog-card-image.loaded {
  filter: none;
  opacity: 1;
  animation: none;
}

.blog-card-image.error {
  filter: grayscale(1);
  opacity: 0.8;
  position: relative;
}

.blog-card-image.error::after {
  content: '⚠️ Image unavailable';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  white-space: nowrap;
}

/* Shimmer animation for loading images */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Optimize image rendering */
.blog-card-image,
.post-image,
.author-avatar {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  backface-visibility: hidden;
  transform: translateZ(0);
}

/* Lazy loading placeholder styles */
.image-placeholder {
  background: linear-gradient(135deg, #3b82f6 0%, #10b981 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 0.875rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.image-placeholder::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: loading-shine 2s infinite;
}

@keyframes loading-shine {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.blog-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.blog-card-image-container {
  position: relative;
  overflow: hidden;
}

.blog-card-image-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.blog-card:hover .blog-card-image-container::after {
  opacity: 1;
}

.blog-card-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 0.75rem 0.75rem 0 0;
}

.blog-card-content {
  padding: 1.5rem;
}

.blog-card-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.875rem;
  color: var(--text-muted);
  margin-bottom: 1rem;
}

.blog-card-category {
  background: var(--gradient-primary);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.blog-card-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.75rem;
  line-height: 1.4;
}

.blog-card-excerpt {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 1rem;
}

.blog-card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 1rem;
  border-top: 1px solid var(--border);
}

.blog-card-author {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-muted);
}

.blog-card-date {
  font-size: 0.875rem;
  color: var(--text-muted);
}

.blog-card-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.tag {
  background: var(--surface);
  color: var(--text-secondary);
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid var(--border);
}

/* Blog Controls */
.blog-controls {
  margin-bottom: 3rem;
}

.search-container {
  position: relative;
  flex: 1;
  max-width: 28rem;
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1.25rem;
  height: 1.25rem;
  color: var(--text-muted);
}

.filter-buttons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 0.5rem 1rem;
  border: 1px solid var(--border);
  background: white;
  color: var(--text-secondary);
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-btn:hover {
  background: var(--surface);
  border-color: var(--primary-blue);
  color: var(--primary-blue);
}

.filter-btn.active {
  background: var(--gradient-primary);
  color: white;
  border-color: transparent;
}

/* Blog Post Page Styles */
.blog-post-header {
  margin-bottom: 3rem;
}

.breadcrumb {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  margin-bottom: 1.5rem;
}

.breadcrumb a {
  color: var(--primary-blue);
  text-decoration: none;
  transition: color 0.2s ease;
}

.breadcrumb a:hover {
  color: var(--primary-blue-dark);
}

.breadcrumb-separator {
  margin: 0 0.5rem;
  color: var(--text-muted);
}

.post-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.category-tag {
  background: var(--gradient-primary);
  color: white;
  padding: 0.375rem 0.875rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.reading-time {
  color: var(--text-muted);
  font-size: 0.875rem;
}

.post-title {
  font-size: 2.5rem;
  font-weight: 900;
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.post-excerpt {
  font-size: 1.25rem;
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 2rem;
}

/* Reading Progress Indicator */
.reading-progress {
  position: fixed;
  top: 0;
  left: 0;
  width: 0%;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #10b981);
  z-index: 100;
  transition: width 0.2s ease-out;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
}



/* Smooth scrolling for the entire page */
html {
  scroll-behavior: smooth;
}

/* Social Sharing */
.social-sharing {
  position: sticky;
  top: 6rem;
  float: right;
  margin-left: 2rem;
  z-index: 40;
}

.social-sharing-container {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(24px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.75rem;
  padding: 0.75rem;
  box-shadow: var(--shadow-lg);
}

.share-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border: none;
  background: white;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-sm);
}

.share-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.share-btn svg {
  width: 1.25rem;
  height: 1.25rem;
}



/* Blog Grid Layout */
.blog-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 2rem;
}

/* Featured Post Styles */
.featured-post {
  grid-column: span 2;
}

.featured-post .blog-card-image {
  height: 300px;
}

.featured-post .blog-card-title {
  font-size: 1.5rem;
}

/* Blog Search Results */
.search-results {
  margin-top: 2rem;
}

.search-results-count {
  color: var(--text-muted);
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.no-results {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--text-muted);
}

.no-results h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: var(--text-secondary);
}

/* Blog Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(2rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.blog-card {
  animation: fadeInUp 0.6s ease forwards;
}

.blog-card:nth-child(2) {
  animation-delay: 0.1s;
}

.blog-card:nth-child(3) {
  animation-delay: 0.2s;
}

.blog-card:nth-child(4) {
  animation-delay: 0.3s;
}



/* Author Bio Section */
.author-bio {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(16, 185, 129, 0.05) 100%);
  border-radius: 0.75rem;
  padding: 1.5rem;
  border: 1px solid rgba(59, 130, 246, 0.1);
  margin-bottom: 2rem;
}

.author-bio .author-avatar {
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid white;
  box-shadow: var(--shadow-md);
}

.author-bio h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.author-bio p {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 1rem;
}

.author-bio .author-social {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.author-bio .author-social a {
  color: var(--text-muted);
  transition: color 0.2s ease;
}

.author-bio .author-social a:hover {
  color: var(--primary-blue);
}

/* Post Navigation */
.post-navigation-section {
  background: var(--surface);
  padding: 2rem 0;
}

.post-nav-link {
  display: flex;
  align-items: center;
  padding: 1rem;
  background: white;
  border-radius: 0.75rem;
  box-shadow: var(--shadow-md);
  text-decoration: none;
  transition: all 0.3s ease;
  max-width: 24rem;
  border: 1px solid var(--border);
}

.post-nav-link:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
  border-color: var(--primary-blue);
}

.post-nav-prev {
  text-align: left;
}

.post-nav-next {
  text-align: right;
  margin-left: auto;
}

.post-nav-link .nav-direction {
  font-size: 0.875rem;
  color: var(--text-muted);
  margin-bottom: 0.25rem;
  display: block;
}

.post-nav-link .nav-title {
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  transition: color 0.2s ease;
}

.post-nav-link:hover .nav-title {
  color: var(--primary-blue);
}

.post-nav-link svg {
  flex-shrink: 0;
  transition: all 0.2s ease;
}

.post-nav-link:hover svg {
  color: var(--primary-blue);
}

.post-nav-prev svg {
  margin-right: 0.75rem;
}

.post-nav-next svg {
  margin-left: 0.75rem;
}

/* Line clamp utility for text truncation */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}



/* Mobile-First Responsive Design */

/* Touch-friendly improvements for all mobile devices */
@media (max-width: 1023px) {
  /* Improve touch targets */
  .blog-card {
    cursor: pointer;
    -webkit-tap-highlight-color: rgba(59, 130, 246, 0.1);
    transition: all 0.2s ease;
  }

  .blog-card:active {
    transform: translateY(-2px) scale(0.98);
  }

  /* Better touch targets for filter buttons */
  .filter-btn {
    min-height: 44px;
    min-width: 44px;
    -webkit-tap-highlight-color: rgba(59, 130, 246, 0.1);
  }

  /* Improve search input on mobile */
  .form-input {
    -webkit-appearance: none;
    border-radius: 0.75rem;
  }

  /* Mobile social sharing transitions */
  .mobile-share-btn {
    transition: all 0.2s ease;
  }

  .mobile-share-btn:active {
    transform: translateY(-2px) scale(0.95);
  }
}

/* Mobile optimizations (up to 640px) */
@media (max-width: 639px) {
  /* Blog Hero Section Mobile */
  .bg-mesh {
    padding-top: 6rem;
    padding-bottom: 3rem;
  }

  /* Blog Controls Mobile */
  .blog-controls {
    margin-bottom: 2rem;
  }

  .blog-controls > div {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .search-container {
    max-width: none;
    order: 1;
  }

  .filter-buttons {
    order: 2;
    justify-content: flex-start;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .filter-btn {
    font-size: 0.8125rem;
    padding: 0.5rem 0.875rem;
    min-height: 44px; /* Better touch target */
    white-space: nowrap;
    flex: 0 0 auto;
  }

  /* Blog Grid Mobile */
  .blog-grid {
    grid-template-columns: 1fr;
    gap: 1.25rem;
  }

  /* Blog Card Mobile */
  .blog-card {
    margin-bottom: 0;
    border-radius: 0.75rem;
  }

  .blog-card-image {
    height: 180px;
  }

  .blog-card-content {
    padding: 1.25rem;
  }

  .blog-card-meta {
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
  }

  .blog-card-category {
    font-size: 0.6875rem;
    padding: 0.25rem 0.625rem;
  }

  .blog-card-title {
    font-size: 1.125rem;
    line-height: 1.3;
    margin-bottom: 0.5rem;
  }

  .blog-card-excerpt {
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 0.75rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .blog-card-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
    padding-top: 0.75rem;
  }

  .blog-card-author {
    font-size: 0.8125rem;
  }

  .blog-card-date {
    font-size: 0.8125rem;
  }

  .blog-card-tags {
    align-self: flex-start;
    width: 100%;
  }

  .tag {
    font-size: 0.6875rem;
    padding: 0.1875rem 0.4375rem;
  }

  /* Individual Blog Post Mobile */
  .post-title {
    font-size: 1.75rem;
    line-height: 1.2;
    margin-bottom: 1rem;
  }

  .post-excerpt {
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
  }

  .post-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
  }

  .category-tag {
    font-size: 0.8125rem;
    padding: 0.3125rem 0.75rem;
  }

  .reading-time {
    font-size: 0.8125rem;
  }

  /* Hide desktop-only elements on mobile */
  .social-sharing {
    display: none;
  }



  /* Mobile Social Sharing */
  .mobile-social-sharing {
    position: fixed;
    bottom: 6rem;
    right: 1.5rem;
    z-index: 40;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    transition: all 0.3s ease;
  }

  .mobile-share-btn {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    background: white;
    border: 1px solid var(--border);
    box-shadow: var(--shadow-md);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    -webkit-tap-highlight-color: transparent;
  }

  .mobile-share-btn:hover,
  .mobile-share-btn:focus {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    outline: none;
  }

  .mobile-share-btn:active {
    transform: translateY(0) scale(0.95);
  }

  .mobile-share-btn svg {
    width: 1.125rem;
    height: 1.125rem;
    transition: transform 0.2s ease;
  }

  .mobile-share-btn:hover svg {
    transform: scale(1.1);
  }

  /* Success state for copy button */
  .mobile-share-btn.success {
    background: #10b981;
    border-color: #10b981;
    transform: scale(1.1);
  }

  .mobile-share-btn.success svg {
    color: white;
  }

  /* Author Bio Mobile */
  .author-bio {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .author-bio .author-avatar {
    width: 3rem;
    height: 3rem;
  }

  .author-bio h3 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
  }

  .author-bio p {
    font-size: 0.875rem;
    line-height: 1.5;
  }

  /* Post Navigation Mobile */
  .post-navigation-section {
    padding: 1.5rem 0;
  }

  .post-nav-link {
    max-width: none;
    padding: 1rem;
    margin-bottom: 1rem;
  }

  .post-nav-link .nav-direction {
    font-size: 0.8125rem;
  }

  .post-nav-link .nav-title {
    font-size: 0.875rem;
    line-height: 1.3;
  }

  .post-nav-prev,
  .post-nav-next {
    text-align: left;
    margin-left: 0;
  }

  /* Related Posts Mobile */
  .related-posts {
    padding: 2.5rem 0;
  }

  .related-posts h2 {
    font-size: 1.75rem;
    margin-bottom: 1.5rem;
  }

  /* Search Results Mobile */
  .search-results-count {
    font-size: 0.8125rem;
    margin-bottom: 1rem;
    text-align: center;
  }

  .no-results {
    padding: 2rem 1rem;
  }

  .no-results h3 {
    font-size: 1.25rem;
    margin-bottom: 0.75rem;
  }

  .no-results p {
    font-size: 0.875rem;
    margin-bottom: 1rem;
  }

  /* Loading State Mobile */
  .loading-state {
    padding: 2rem 1rem;
  }

  /* Featured Post Mobile - Remove special styling */
  .featured-post {
    grid-column: span 1;
  }

  .featured-post .blog-card-image {
    height: 180px;
  }

  .featured-post .blog-card-title {
    font-size: 1.125rem;
  }
}

/* Small tablets (640px - 767px) */
@media (min-width: 640px) and (max-width: 767px) {
  .blog-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .blog-card-image {
    height: 160px;
  }

  .filter-buttons {
    justify-content: center;
  }

  .post-title {
    font-size: 2rem;
  }

  .post-excerpt {
    font-size: 1.125rem;
  }
}

/* Tablets (768px - 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
  .blog-controls > div {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }

  .search-container {
    max-width: 20rem;
  }

  .blog-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .featured-post {
    grid-column: span 2;
  }

  .featured-post .blog-card-image {
    height: 240px;
  }

  .social-sharing {
    display: none;
  }

  /* Show mobile social sharing on tablets */
  .mobile-social-sharing {
    display: flex;
  }

  .post-title {
    font-size: 2.25rem;
  }

  .post-meta {
    flex-direction: row;
    align-items: center;
    gap: 1rem;
  }
}

/* Desktop (1024px and up) */
@media (min-width: 1024px) {
  .blog-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }

  .featured-post {
    grid-column: span 2;
  }

  .featured-post .blog-card-image {
    height: 300px;
  }

  .featured-post .blog-card-title {
    font-size: 1.5rem;
  }

  .social-sharing {
    display: block;
  }

  .mobile-social-sharing {
    display: none;
  }

  .post-title {
    font-size: 2.5rem;
  }

  .post-excerpt {
    font-size: 1.25rem;
  }

  .post-meta {
    flex-direction: row;
    align-items: center;
    gap: 1rem;
  }
}

/* Large Desktop (1280px and up) */
@media (min-width: 1280px) {
  .blog-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2.5rem;
  }

  .featured-post .blog-card-image {
    height: 320px;
  }
}/* Adv
anced Performance Optimizations */

/* GPU acceleration for smooth animations */
.blog-card,
.blog-card-image,
.filter-btn,
.share-btn,
.mobile-share-btn {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Optimize repaints and reflows */
.blog-card:hover {
  will-change: transform, box-shadow;
}

.blog-card-image {
  will-change: opacity, filter;
}

.reading-progress {
  will-change: width;
  contain: layout style paint;
}

/* Reduce layout thrashing */
.blog-grid {
  contain: layout style;
}

.blog-card-content {
  contain: layout style paint;
}

/* Optimize font rendering */
.blog-card-title,
.post-title,
.blog-card-excerpt {
  text-rendering: optimizeSpeed;
  font-display: swap;
}

/* Critical rendering path optimizations */
.blog-card-image,
.post-image {
  content-visibility: auto;
  contain-intrinsic-size: 400px 200px;
}

/* Optimize scroll performance */
.social-sharing {
  contain: layout style paint;
}

/* Reduce paint complexity */
.blog-card::before,
.blog-card::after {
  content: none;
}

/* Optimize animations for 60fps */
@media (prefers-reduced-motion: no-preference) {
  .blog-card {
    transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1),
                box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .blog-card-image {
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                filter 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

/* Respect user preferences for reduced motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .blog-card {
    transform: none !important;
  }
  
  .shimmer,
  .loading-shine {
    animation: none !important;
  }
}

/* Optimize for high DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .blog-card-image,
  .post-image,
  .author-avatar {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Container queries for responsive images (future enhancement) */
@supports (container-type: inline-size) {
  .blog-card {
    container-type: inline-size;
  }
  
  @container (max-width: 300px) {
    .blog-card-image {
      height: 150px;
    }
    
    .blog-card-title {
      font-size: 1rem;
    }
  }
}

/* Optimize for print */
@media print {
  .blog-card {
    break-inside: avoid;
    page-break-inside: avoid;
  }
  
  .blog-card-image {
    max-height: 200px;
    object-fit: contain;
  }
  
  .mobile-social-sharing,
  .social-sharing {
    display: none !important;
  }
}

/* Performance hints for browsers */
.blog-grid {
  /* Hint to browser about layout stability */
  grid-template-rows: masonry; /* Future CSS feature */
}

/* Optimize critical rendering path */
.above-fold {
  contain: layout style paint;
}

.below-fold {
  content-visibility: auto;
  contain-intrinsic-size: 0 500px;
}

/* Intersection observer optimizations */
.lazy-section {
  min-height: 100px;
  content-visibility: auto;
}

/* Optimize for Core Web Vitals */
.blog-card-image[loading="lazy"] {
  /* Prevent layout shift */
  min-height: 200px;
  background: #f3f4f6;
}

/* Reduce Cumulative Layout Shift */
.blog-card {
  /* Reserve space to prevent layout shift */
  min-height: 400px;
}

.featured-post {
  min-height: 500px;
}

/* Optimize text rendering */
.blog-card-title,
.blog-card-excerpt {
  /* Prevent text from causing layout shifts */
  overflow-wrap: break-word;
  hyphens: auto;
}

/* Memory optimization */
.blog-card:not(.visible) {
  /* Reduce memory usage for off-screen cards */
  transform: translateZ(0) scale(0.99);
}
/*
 Blog Card Image Container */
.blog-card-image-container {
  width: 100%;
  height: 200px;
  overflow: hidden;
  position: relative;
}

.blog-card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.blog-card:hover .blog-card-image {
  transform: scale(1.05);
}

/* Blog Card Content */
.blog-card-content {
  padding: 1.5rem;
}

.blog-card-meta {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.blog-card-category {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.blog-card-date,
.reading-time {
  color: #6b7280;
  font-size: 0.875rem;
}

.blog-card-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 0.75rem;
  line-height: 1.4;
}

.blog-card-excerpt {
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.blog-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.blog-card-author {
  font-size: 0.875rem;
  color: #374151;
  font-weight: 500;
}

.blog-card-tags {
  display: flex;
  gap: 0.5rem;
}

.tag {
  background: #f3f4f6;
  color: #6b7280;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Blog Card Hover Effects */
.blog-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.blog-card:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Featured Post Styling */
.featured-post {
  grid-column: span 2;
}

@media (max-width: 768px) {
  .featured-post {
    grid-column: span 1;
  }
}

.featured-post .blog-card-image-container {
  height: 300px;
}

/* Loading State */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* Filter Buttons */
.filter-buttons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  background: white;
  color: #6b7280;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-btn:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.filter-btn.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.touch-target {
  min-height: 44px;
  min-width: 44px;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .blog-card-content {
    padding: 1rem;
  }
  
  .blog-card-title {
    font-size: 1.125rem;
  }
  
  .filter-btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.8125rem;
  }
}