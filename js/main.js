// DocForge AI - Main JavaScript
// Contact Form Validation and Interactions

document.addEventListener('DOMContentLoaded', function () {
    // Initialize all functionality
    initMobileMenu();
    initSmoothScrolling();
    initAnimations();
    initNavbarScroll();
    initCounterAnimation();
    initContactForm();
    initTableOfContents();
});

// Enhanced mobile menu toggle with better UX
function initMobileMenu() {
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileMenu = document.getElementById('mobile-menu');

    if (mobileMenuBtn && mobileMenu) {
        // Toggle menu visibility
        mobileMenuBtn.addEventListener('click', function (e) {
            e.preventDefault();
            const isHidden = mobileMenu.classList.contains('hidden');

            if (isHidden) {
                mobileMenu.classList.remove('hidden');
                mobileMenuBtn.setAttribute('aria-expanded', 'true');
                // Change hamburger to X icon
                mobileMenuBtn.innerHTML = `
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                `;
                // Prevent body scroll when menu is open
                document.body.style.overflow = 'hidden';
            } else {
                mobileMenu.classList.add('hidden');
                mobileMenuBtn.setAttribute('aria-expanded', 'false');
                // Change X back to hamburger icon
                mobileMenuBtn.innerHTML = `
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                `;
                // Restore body scroll
                document.body.style.overflow = '';
            }
        });

        // Close menu when clicking on a link
        const menuLinks = mobileMenu.querySelectorAll('a');
        menuLinks.forEach(link => {
            link.addEventListener('click', function () {
                mobileMenu.classList.add('hidden');
                mobileMenuBtn.setAttribute('aria-expanded', 'false');
                mobileMenuBtn.innerHTML = `
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                `;
                document.body.style.overflow = '';
            });
        });

        // Close menu when clicking outside
        document.addEventListener('click', function (e) {
            if (!mobileMenuBtn.contains(e.target) && !mobileMenu.contains(e.target)) {
                if (!mobileMenu.classList.contains('hidden')) {
                    mobileMenu.classList.add('hidden');
                    mobileMenuBtn.setAttribute('aria-expanded', 'false');
                    mobileMenuBtn.innerHTML = `
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    `;
                    document.body.style.overflow = '';
                }
            }
        });

        // Close menu on escape key
        document.addEventListener('keydown', function (e) {
            if (e.key === 'Escape' && !mobileMenu.classList.contains('hidden')) {
                mobileMenu.classList.add('hidden');
                mobileMenuBtn.setAttribute('aria-expanded', 'false');
                mobileMenuBtn.innerHTML = `
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                `;
                document.body.style.overflow = '';
                mobileMenuBtn.focus();
            }
        });

        // Handle window resize with debouncing
        let resizeTimeout;
        window.addEventListener('resize', function () {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                if (window.innerWidth >= 768) { // md breakpoint
                    mobileMenu.classList.add('hidden');
                    mobileMenuBtn.setAttribute('aria-expanded', 'false');
                    mobileMenuBtn.innerHTML = `
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    `;
                    document.body.style.overflow = '';
                }
            }, 100);
        });

        // Set initial aria-expanded state
        mobileMenuBtn.setAttribute('aria-expanded', 'false');
    }
}

// Enhanced smooth scrolling for navigation links
function initSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            const href = this.getAttribute('href');

            // Skip empty anchors or just "#"
            if (!href || href === '#') {
                return;
            }

            e.preventDefault();
            const target = document.querySelector(href);

            if (target) {
                // Calculate offset for fixed navigation
                const navHeight = document.querySelector('nav')?.offsetHeight || 80;
                const targetPosition = target.offsetTop - navHeight - 20; // Extra 20px padding

                // Use smooth scrolling with custom offset
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });

                // Update URL hash after scrolling completes
                setTimeout(() => {
                    if (history.pushState) {
                        history.pushState(null, null, href);
                    }
                }, 800);

                // Close mobile menu if open
                const mobileMenu = document.getElementById('mobile-menu');
                const mobileMenuBtn = document.getElementById('mobile-menu-btn');
                if (mobileMenu && !mobileMenu.classList.contains('hidden')) {
                    mobileMenu.classList.add('hidden');
                    if (mobileMenuBtn) {
                        mobileMenuBtn.setAttribute('aria-expanded', 'false');
                        mobileMenuBtn.innerHTML = `
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                        `;
                    }
                    document.body.style.overflow = '';
                }
            }
        });
    });
}

// Enhanced Intersection Observer for fade-in animations with stagger effects
function initAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function (entries) {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                // Add stagger delay for elements in the same container
                const delay = index * 100; // 100ms stagger between elements

                setTimeout(() => {
                    entry.target.classList.add('visible');
                }, delay);

                // Stop observing once animated to improve performance
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Observe all elements with fade-in class
    document.querySelectorAll('.fade-in').forEach((element, index) => {
        // Add initial delay based on element position for better visual flow
        element.style.transitionDelay = `${index * 50}ms`;
        observer.observe(element);
    });

    // Add scroll-triggered animations for other elements
    const scrollElements = document.querySelectorAll('.card, .glass-card');
    scrollElements.forEach(element => {
        if (!element.classList.contains('fade-in')) {
            element.style.opacity = '0';
            element.style.transform = 'translateY(20px)';
            element.style.transition = 'all 0.6s ease-out';

            const scrollObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                        scrollObserver.unobserve(entry.target);
                    }
                });
            }, { threshold: 0.1 });

            scrollObserver.observe(element);
        }
    });
}

// Navbar background on scroll
function initNavbarScroll() {
    window.addEventListener('scroll', function () {
        const nav = document.querySelector('nav');
        if (nav) {
            if (window.scrollY > 100) {
                nav.style.background = 'rgba(255, 255, 255, 0.98)';
                nav.classList.add('shadow-lg');
            } else {
                nav.style.background = 'rgba(255, 255, 255, 0.95)';
                nav.classList.remove('shadow-lg');
            }
        }
    });
}

// Enhanced animated counter for messages answered today
function initCounterAnimation() {
    function animateCounter(elementId, targetValue, duration = 2000) {
        const element = document.getElementById(elementId);
        if (!element) return;

        const startValue = 0;
        const increment = targetValue / (duration / 16); // 60fps
        let currentValue = startValue;

        function updateCounter() {
            currentValue += increment;
            if (currentValue >= targetValue) {
                element.textContent = targetValue;
                // Add completion animation
                element.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    element.style.transform = 'scale(1)';
                    element.style.transition = 'transform 0.3s ease';
                }, 100);
            } else {
                element.textContent = Math.floor(currentValue);
                requestAnimationFrame(updateCounter);
            }
        }

        // Add initial delay for better visual effect
        setTimeout(() => {
            updateCounter();
        }, 500);
    }

    // Start counter animation when page loads or when element comes into view
    const counterElement = document.getElementById('messages-counter');
    if (counterElement) {
        const counterObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounter('messages-counter', 127);
                    counterObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });

        counterObserver.observe(counterElement);
    }
}

// Contact Form Validation and Handling
function initContactForm() {
    const form = document.getElementById('contactForm');
    if (!form) return; // Exit if form doesn't exist on this page

    const submitBtn = document.getElementById('submitBtn');
    const submitText = document.getElementById('submitText');
    const resetBtn = document.getElementById('resetBtn');
    const messageTextarea = document.getElementById('message');
    const messageCount = document.getElementById('message-count');
    const successMessage = document.getElementById('form-success');
    const errorMessage = document.getElementById('form-error');

    // Character counter for message field
    if (messageTextarea && messageCount) {
        messageTextarea.addEventListener('input', function () {
            const currentLength = this.value.length;
            const maxLength = 1000;
            messageCount.textContent = `${currentLength} / ${maxLength} characters`;

            if (currentLength > maxLength) {
                messageCount.style.color = '#ef4444';
            } else if (currentLength >= 900) {
                messageCount.style.color = '#f59e0b'; // Warning color
            } else {
                messageCount.style.color = '#6b7280';
            }
        });
    }

    // Enhanced validation functions
    function validateName(name) {
        const trimmed = name.trim();
        if (trimmed.length < 2) return { valid: false, message: 'Name must be at least 2 characters long' };
        if (trimmed.length > 50) return { valid: false, message: 'Name must be no more than 50 characters long' };
        if (!/^[a-zA-Z\s'-]+$/.test(trimmed)) return { valid: false, message: 'Name can only contain letters, spaces, hyphens, and apostrophes' };
        return { valid: true };
    }

    function validateEmail(email) {
        const trimmed = email.trim();
        if (!trimmed) return { valid: false, message: 'Email is required' };

        // Enhanced email regex that handles most valid email formats
        const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

        if (!emailRegex.test(trimmed)) return { valid: false, message: 'Please enter a valid email address' };
        if (trimmed.length > 254) return { valid: false, message: 'Email address is too long' };

        return { valid: true };
    }

    function validateSubject(subject) {
        if (!subject || subject === '') return { valid: false, message: 'Please select a subject' };
        return { valid: true };
    }

    function validateMessage(message) {
        const trimmed = message.trim();
        if (!trimmed) return { valid: false, message: 'Message is required' };
        if (trimmed.length < 10) return { valid: false, message: 'Message must be at least 10 characters long' };
        if (trimmed.length > 1000) return { valid: false, message: 'Message must be no more than 1000 characters long' };
        return { valid: true };
    }

    function validateInquiryType(inquiryType) {
        if (!inquiryType) return { valid: false, message: 'Please select an inquiry type' };
        const validTypes = ['sales', 'support', 'general'];
        if (!validTypes.includes(inquiryType)) return { valid: false, message: 'Please select a valid inquiry type' };
        return { valid: true };
    }

    // Enhanced field validation display
    function showFieldError(fieldId, message) {
        const field = document.getElementById(fieldId);
        const errorDiv = document.getElementById(fieldId + '-error');

        if (field && errorDiv) {
            field.classList.add('error');
            field.classList.remove('success');
            field.style.borderColor = '#ef4444';
            field.style.boxShadow = '0 0 0 3px rgba(239, 68, 68, 0.1)';

            errorDiv.textContent = message;
            errorDiv.classList.remove('hidden');
            errorDiv.setAttribute('aria-live', 'polite');
        }
    }

    function showFieldSuccess(fieldId) {
        const field = document.getElementById(fieldId);
        const errorDiv = document.getElementById(fieldId + '-error');

        if (field && errorDiv) {
            field.classList.remove('error');
            field.classList.add('success');
            field.style.borderColor = '#10b981';
            field.style.boxShadow = '0 0 0 3px rgba(16, 185, 129, 0.1)';

            errorDiv.classList.add('hidden');
        }
    }

    function clearFieldValidation(fieldId) {
        const field = document.getElementById(fieldId);
        const errorDiv = document.getElementById(fieldId + '-error');

        if (field && errorDiv) {
            field.classList.remove('error', 'success');
            field.style.borderColor = '';
            field.style.boxShadow = '';
            errorDiv.classList.add('hidden');
        }
    }

    function showInquiryTypeError(message) {
        const errorDiv = document.getElementById('inquiry-type-error');
        if (errorDiv) {
            errorDiv.textContent = message;
            errorDiv.classList.remove('hidden');
            errorDiv.setAttribute('aria-live', 'polite');
        }
    }

    function clearInquiryTypeError() {
        const errorDiv = document.getElementById('inquiry-type-error');
        if (errorDiv) {
            errorDiv.classList.add('hidden');
        }
    }

    // Real-time validation with debouncing
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Real-time validation for name field
    const nameField = document.getElementById('name');
    if (nameField) {
        const validateNameField = debounce(function () {
            const validation = validateName(nameField.value);
            if (nameField.value.trim() === '') {
                clearFieldValidation('name');
            } else if (!validation.valid) {
                showFieldError('name', validation.message);
            } else {
                showFieldSuccess('name');
            }
        }, 300);

        nameField.addEventListener('input', validateNameField);
        nameField.addEventListener('blur', function () {
            const validation = validateName(this.value);
            if (!validation.valid) {
                showFieldError('name', validation.message);
            } else if (this.value.trim() !== '') {
                showFieldSuccess('name');
            }
        });
    }

    // Real-time validation for email field
    const emailField = document.getElementById('email');
    if (emailField) {
        const validateEmailField = debounce(function () {
            const validation = validateEmail(emailField.value);
            if (emailField.value.trim() === '') {
                clearFieldValidation('email');
            } else if (!validation.valid) {
                showFieldError('email', validation.message);
            } else {
                showFieldSuccess('email');
            }
        }, 300);

        emailField.addEventListener('input', validateEmailField);
        emailField.addEventListener('blur', function () {
            const validation = validateEmail(this.value);
            if (!validation.valid) {
                showFieldError('email', validation.message);
            } else {
                showFieldSuccess('email');
            }
        });
    }

    // Subject field validation
    const subjectField = document.getElementById('subject');
    if (subjectField) {
        subjectField.addEventListener('change', function () {
            const validation = validateSubject(this.value);
            if (!validation.valid) {
                showFieldError('subject', validation.message);
            } else {
                showFieldSuccess('subject');
            }
        });
    }

    // Message field validation with real-time feedback
    if (messageTextarea) {
        const validateMessageField = debounce(function () {
            const validation = validateMessage(messageTextarea.value);
            if (messageTextarea.value.trim() === '') {
                clearFieldValidation('message');
            } else if (!validation.valid) {
                showFieldError('message', validation.message);
            } else {
                showFieldSuccess('message');
            }
        }, 300);

        messageTextarea.addEventListener('input', function () {
            validateMessageField();
            // Update character counter (already handled above)
        });

        messageTextarea.addEventListener('blur', function () {
            const validation = validateMessage(this.value);
            if (!validation.valid) {
                showFieldError('message', validation.message);
            } else if (this.value.trim() !== '') {
                showFieldSuccess('message');
            }
        });
    }

    // Inquiry type validation
    const inquiryRadios = document.querySelectorAll('input[name="inquiryType"]');
    inquiryRadios.forEach(radio => {
        radio.addEventListener('change', function () {
            clearInquiryTypeError();
        });
    });

    // Mobile-specific form enhancements
    function optimizeForMobile() {
        // Prevent zoom on iOS when focusing inputs
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            if (input.type !== 'radio' && input.type !== 'checkbox') {
                // Ensure font-size is at least 16px to prevent zoom
                const computedStyle = window.getComputedStyle(input);
                const fontSize = parseFloat(computedStyle.fontSize);
                if (fontSize < 16) {
                    input.style.fontSize = '16px';
                }
            }
        });

        // Improve touch targets for radio buttons on mobile
        if (window.innerWidth < 768) {
            const radioLabels = form.querySelectorAll('label:has(input[type="radio"])');
            radioLabels.forEach(label => {
                label.style.minHeight = '48px';
                label.style.padding = '12px 16px';
            });
        }

        // Add mobile-specific keyboard handling
        const textInputs = form.querySelectorAll('input[type="text"], input[type="email"], textarea');
        textInputs.forEach(input => {
            input.addEventListener('focus', function () {
                // Scroll input into view on mobile
                if (window.innerWidth < 768) {
                    setTimeout(() => {
                        this.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }, 300); // Wait for keyboard to appear
                }
            });
        });
    }

    // Call mobile optimization
    optimizeForMobile();

    // Re-optimize on window resize
    window.addEventListener('resize', debounce(optimizeForMobile, 250));

    // Enhanced form submission with comprehensive validation
    if (form) {
        form.addEventListener('submit', function (e) {
            e.preventDefault();

            // Hide previous messages
            if (successMessage) successMessage.classList.add('hidden');
            if (errorMessage) errorMessage.classList.add('hidden');

            // Get form data
            const formData = new FormData(form);
            const name = formData.get('name') || '';
            const email = formData.get('email') || '';
            const subject = formData.get('subject') || '';
            const message = formData.get('message') || '';
            const inquiryType = formData.get('inquiryType') || '';

            let isValid = true;
            const errors = [];

            // Validate all fields
            const nameValidation = validateName(name);
            if (!nameValidation.valid) {
                showFieldError('name', nameValidation.message);
                errors.push('Name: ' + nameValidation.message);
                isValid = false;
            } else {
                showFieldSuccess('name');
            }

            const emailValidation = validateEmail(email);
            if (!emailValidation.valid) {
                showFieldError('email', emailValidation.message);
                errors.push('Email: ' + emailValidation.message);
                isValid = false;
            } else {
                showFieldSuccess('email');
            }

            const subjectValidation = validateSubject(subject);
            if (!subjectValidation.valid) {
                showFieldError('subject', subjectValidation.message);
                errors.push('Subject: ' + subjectValidation.message);
                isValid = false;
            } else {
                showFieldSuccess('subject');
            }

            const messageValidation = validateMessage(message);
            if (!messageValidation.valid) {
                showFieldError('message', messageValidation.message);
                errors.push('Message: ' + messageValidation.message);
                isValid = false;
            } else {
                showFieldSuccess('message');
            }

            const inquiryValidation = validateInquiryType(inquiryType);
            if (!inquiryValidation.valid) {
                showInquiryTypeError(inquiryValidation.message);
                errors.push('Inquiry Type: ' + inquiryValidation.message);
                isValid = false;
            } else {
                clearInquiryTypeError();
            }

            if (!isValid) {
                if (errorMessage) {
                    errorMessage.classList.remove('hidden');
                    errorMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }

                // Focus on first error field with mobile-friendly scrolling
                const firstErrorField = form.querySelector('.error');
                if (firstErrorField) {
                    // On mobile, scroll to field with some offset for better UX
                    if (window.innerWidth < 768) {
                        firstErrorField.scrollIntoView({
                            behavior: 'smooth',
                            block: 'center',
                            inline: 'nearest'
                        });
                        // Delay focus to ensure scrolling completes
                        setTimeout(() => {
                            firstErrorField.focus();
                        }, 300);
                    } else {
                        firstErrorField.focus();
                    }
                }

                return;
            }

            // Show loading state
            if (submitBtn && submitText) {
                submitBtn.disabled = true;
                submitText.textContent = 'Sending...';
                submitBtn.classList.add('opacity-75');

                // Add loading spinner
                const spinner = document.createElement('div');
                spinner.className = 'animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2';
                spinner.id = 'loading-spinner';
                submitBtn.insertBefore(spinner, submitText);
            }

            // Simulate form submission (replace with actual API call)
            setTimeout(() => {
                // Reset button state
                if (submitBtn && submitText) {
                    submitBtn.disabled = false;
                    submitText.textContent = 'Send Message';
                    submitBtn.classList.remove('opacity-75');

                    // Remove loading spinner
                    const spinner = document.getElementById('loading-spinner');
                    if (spinner) {
                        spinner.remove();
                    }
                }

                // Show success message
                if (successMessage) {
                    successMessage.classList.remove('hidden');
                    successMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }

                // Reset form
                form.reset();
                if (messageCount) {
                    messageCount.textContent = '0 / 1000 characters';
                    messageCount.style.color = '#6b7280';
                }

                // Clear all validation states
                ['name', 'email', 'subject', 'message'].forEach(fieldId => {
                    clearFieldValidation(fieldId);
                });
                clearInquiryTypeError();

                // Add success animation
                if (successMessage) {
                    successMessage.style.transform = 'scale(0.95)';
                    successMessage.style.opacity = '0';
                    setTimeout(() => {
                        successMessage.style.transform = 'scale(1)';
                        successMessage.style.opacity = '1';
                        successMessage.style.transition = 'all 0.3s ease';
                    }, 100);
                }

            }, 2000); // Simulate 2 second delay
        });
    }

    // Enhanced reset button functionality with animation
    if (resetBtn) {
        resetBtn.addEventListener('click', function (e) {
            e.preventDefault();

            // Add confirmation for reset if form has data
            const hasData = form.querySelector('input[type="text"]').value.trim() !== '' ||
                form.querySelector('input[type="email"]').value.trim() !== '' ||
                form.querySelector('select').value !== '' ||
                form.querySelector('textarea').value.trim() !== '' ||
                form.querySelector('input[type="radio"]:checked');

            if (hasData) {
                if (!confirm('Are you sure you want to clear all form data?')) {
                    return;
                }
            }

            // Add reset animation
            form.style.opacity = '0.5';
            form.style.transform = 'scale(0.98)';
            form.style.transition = 'all 0.2s ease';

            setTimeout(() => {
                // Reset form
                form.reset();

                // Clear all validation states
                ['name', 'email', 'subject', 'message'].forEach(fieldId => {
                    clearFieldValidation(fieldId);
                });
                clearInquiryTypeError();

                // Reset character counter
                if (messageCount) {
                    messageCount.textContent = '0 / 1000 characters';
                    messageCount.style.color = '#6b7280';
                }

                // Hide messages
                if (successMessage) successMessage.classList.add('hidden');
                if (errorMessage) errorMessage.classList.add('hidden');

                // Clear saved data
                clearSavedData();

                // Restore form appearance
                form.style.opacity = '1';
                form.style.transform = 'scale(1)';

                // Focus on first field
                const firstField = form.querySelector('input[type="text"]');
                if (firstField) {
                    setTimeout(() => firstField.focus(), 100);
                }
            }, 200);
        });
    }

    // Keyboard navigation enhancements
    form.addEventListener('keydown', function (e) {
        // Submit form with Ctrl+Enter
        if (e.ctrlKey && e.key === 'Enter') {
            e.preventDefault();
            form.dispatchEvent(new Event('submit'));
        }

        // Reset form with Ctrl+R (prevent page reload)
        if (e.ctrlKey && e.key === 'r') {
            e.preventDefault();
            if (resetBtn) {
                resetBtn.click();
            }
        }
    });

    // Auto-save form data to localStorage (optional enhancement)
    const autoSaveKey = 'docforge-contact-form-draft';

    function saveFormData() {
        const formData = {
            name: document.getElementById('name')?.value || '',
            email: document.getElementById('email')?.value || '',
            subject: document.getElementById('subject')?.value || '',
            message: document.getElementById('message')?.value || '',
            inquiryType: document.querySelector('input[name="inquiryType"]:checked')?.value || ''
        };
        localStorage.setItem(autoSaveKey, JSON.stringify(formData));
    }

    function loadFormData() {
        try {
            const savedData = localStorage.getItem(autoSaveKey);
            if (savedData) {
                const formData = JSON.parse(savedData);

                if (formData.name) document.getElementById('name').value = formData.name;
                if (formData.email) document.getElementById('email').value = formData.email;
                if (formData.subject) document.getElementById('subject').value = formData.subject;
                if (formData.message) {
                    document.getElementById('message').value = formData.message;
                    // Update character counter
                    if (messageCount) {
                        messageCount.textContent = `${formData.message.length} / 1000 characters`;
                    }
                }
                if (formData.inquiryType) {
                    const radio = document.querySelector(`input[name="inquiryType"][value="${formData.inquiryType}"]`);
                    if (radio) radio.checked = true;
                }
            }
        } catch (e) {
            console.warn('Could not load saved form data:', e);
        }
    }

    function clearSavedData() {
        localStorage.removeItem(autoSaveKey);
    }

    // Load saved data on page load
    loadFormData();

    // Save data on input (debounced)
    const saveData = debounce(saveFormData, 1000);
    form.addEventListener('input', saveData);
    form.addEventListener('change', saveData);

    // Clear saved data on successful submission
    form.addEventListener('submit', function () {
        setTimeout(clearSavedData, 2500); // Clear after success message
    });

    // Add sample data for testing (development only)
    function populateTestData() {
        if (document.getElementById('name')) document.getElementById('name').value = 'Sarah Johnson';
        if (document.getElementById('email')) document.getElementById('email').value = '<EMAIL>';
        if (document.getElementById('subject')) document.getElementById('subject').value = 'sales';
        if (document.getElementById('message')) {
            document.getElementById('message').value = 'Hi there! I\'m interested in learning more about DocForge AI for my marketing team. We create a lot of reports and presentations, and I\'d love to see how your AI can help us work more efficiently. Could we schedule a demo?';
            // Update character counter
            if (messageCount) {
                const messageLength = document.getElementById('message').value.length;
                messageCount.textContent = `${messageLength} / 1000 characters`;
            }
        }
        const salesRadio = document.querySelector('input[name="inquiryType"][value="sales"]');
        if (salesRadio) salesRadio.checked = true;
    }

    // Add keyboard shortcut for testing (Ctrl+Shift+T)
    document.addEventListener('keydown', function (e) {
        if (e.ctrlKey && e.shiftKey && e.key === 'T') {
            e.preventDefault();
            populateTestData();
            console.log('Test data populated! You can now test form validation and submission.');
        }
    });
}
// Table of Contents functionality for Terms of Use page
function initTableOfContents() {
    const tocLinks = document.querySelectorAll('.toc-link');
    const sections = document.querySelectorAll('.terms-section');

    if (tocLinks.length === 0 || sections.length === 0) {
        return; // Exit if TOC elements don't exist on this page
    }

    // Smooth scrolling for TOC links
    tocLinks.forEach((link, index) => {
        link.addEventListener('click', function (e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetSection = document.getElementById(targetId);

            if (targetSection) {
                // Calculate offset for fixed navigation
                const navHeight = document.querySelector('nav')?.offsetHeight || 80;
                const targetPosition = targetSection.offsetTop - navHeight - 20;

                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });

                // Update URL hash after scrolling
                setTimeout(() => {
                    if (history.pushState) {
                        history.pushState(null, null, `#${targetId}`);
                    }
                }, 800);

                // Update active state immediately for better UX
                updateActiveSection(targetId);
            }
        });

        // Keyboard navigation support
        link.addEventListener('keydown', function (e) {
            const currentIndex = Array.from(tocLinks).indexOf(this);

            switch (e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    const nextIndex = (currentIndex + 1) % tocLinks.length;
                    tocLinks[nextIndex].focus();
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    const prevIndex = currentIndex === 0 ? tocLinks.length - 1 : currentIndex - 1;
                    tocLinks[prevIndex].focus();
                    break;
                case 'Home':
                    e.preventDefault();
                    tocLinks[0].focus();
                    break;
                case 'End':
                    e.preventDefault();
                    tocLinks[tocLinks.length - 1].focus();
                    break;
            }
        });
    });

    // Intersection Observer for active section highlighting
    const observerOptions = {
        root: null,
        rootMargin: '-100px 0px -50% 0px', // Trigger when section is in upper portion of viewport
        threshold: 0
    };

    const sectionObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const sectionId = entry.target.id;
                updateActiveSection(sectionId);
            }
        });
    }, observerOptions);

    // Observe all sections
    sections.forEach(section => {
        sectionObserver.observe(section);
    });

    // Function to update active section highlighting
    function updateActiveSection(activeSectionId) {
        tocLinks.forEach(link => {
            const sectionId = link.getAttribute('data-section');
            if (sectionId === activeSectionId) {
                link.classList.add('active');
            } else {
                link.classList.remove('active');
            }
        });
    }

    // Handle initial page load with hash
    function handleInitialHash() {
        const hash = window.location.hash;
        if (hash) {
            const sectionId = hash.substring(1);
            const targetSection = document.getElementById(sectionId);
            if (targetSection) {
                // Delay to ensure page is fully loaded
                setTimeout(() => {
                    const navHeight = document.querySelector('nav')?.offsetHeight || 80;
                    const targetPosition = targetSection.offsetTop - navHeight - 20;

                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });

                    updateActiveSection(sectionId);
                }, 100);
            }
        } else {
            // If no hash, highlight first section
            if (sections.length > 0) {
                updateActiveSection(sections[0].id);
            }
        }
    }

    // Handle browser back/forward navigation
    window.addEventListener('popstate', function () {
        handleInitialHash();
    });

    // Initialize on page load
    handleInitialHash();

    // Fallback scroll spy for browsers that don't support Intersection Observer
    if (!window.IntersectionObserver) {
        let ticking = false;

        function updateScrollSpy() {
            const scrollPosition = window.scrollY + 150; // Offset for fixed nav

            let activeSection = null;
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionBottom = sectionTop + section.offsetHeight;

                if (scrollPosition >= sectionTop && scrollPosition < sectionBottom) {
                    activeSection = section.id;
                }
            });

            if (activeSection) {
                updateActiveSection(activeSection);
            }

            ticking = false;
        }

        function requestTick() {
            if (!ticking) {
                requestAnimationFrame(updateScrollSpy);
                ticking = true;
            }
        }

        window.addEventListener('scroll', requestTick);
    }

    // Mobile-specific enhancements
    function optimizeTOCForMobile() {
        if (window.innerWidth < 1024) {
            // Make TOC more touch-friendly on mobile
            tocLinks.forEach(link => {
                link.style.minHeight = '44px'; // Minimum touch target size
                link.style.display = 'flex';
                link.style.alignItems = 'center';
                link.style.justifyContent = 'center';
            });
        }
    }

    // Apply mobile optimizations
    optimizeTOCForMobile();

    // Re-optimize on window resize
    window.addEventListener('resize', debounce(optimizeTOCForMobile, 250));
}

// Enhanced debounce function if not already defined
if (typeof debounce === 'undefined') {
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}