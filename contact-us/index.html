<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Contact Us - DocForge AI</title>
    <meta name="description"
        content="Get in touch with DocForge AI. Contact our sales, support, or general inquiries team for assistance with AI-powered document creation." />
    <link rel="stylesheet" href="../css/styles.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

</head>

<body class="bg-white">
    <!-- Navigation -->
    <nav class="fixed top-0 left-0 right-0 nav-glass z-50 transition-all duration-300">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-20">
                <!-- Logo -->
                <div class="flex items-center">
                    <div class="flex items-center">
                        <svg class="w-10 h-10 mr-3" viewBox="0 0 32 32" fill="currentColor" style="color: #1E3A8A;">
                            <path
                                d="M8 4h16a4 4 0 0 1 4 4v16a4 4 0 0 1-4 4H8a4 4 0 0 1-4-4V8a4 4 0 0 1 4-4zm2 6v12h12V10H10zm2 2h8v2h-8v-2zm0 4h8v2h-8v-2zm0 4h6v2h-6v-2z" />
                        </svg>
                        <a href="../index.html" class="text-2xl font-bold text-gray-900">DocForge AI</a>
                    </div>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="../index.html#features" class="text-gray-600 transition-colors font-medium"
                        style="color: #6B7280;" onmouseover="this.style.color='#1E3A8A'"
                        onmouseout="this.style.color='#6B7280'">Features</a>
                    <a href="../index.html#pricing" class="text-gray-600 transition-colors font-medium"
                        style="color: #6B7280;" onmouseover="this.style.color='#1E3A8A'"
                        onmouseout="this.style.color='#6B7280'">Pricing</a>
                    <a href="index.html" class="text-blue-600 transition-colors font-medium"
                        style="color: #1E3A8A;">Contact</a>
                    <a href="https://docforgeai.netlify.app/" class="text-gray-600 transition-colors font-medium"
                        style="color: #6B7280;" onmouseover="this.style.color='#1E3A8A'"
                        onmouseout="this.style.color='#6B7280'">Sign In</a>
                    <a href="https://docforgeai.netlify.app/" class="btn-primary px-3 py-1">Start Free Trial</a>
                </div>

                <!-- Mobile Menu Button -->
                <button class="md:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors" id="mobile-menu-btn"
                    aria-label="Toggle navigation menu" aria-expanded="false" aria-controls="mobile-menu">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div class="md:hidden hidden" id="mobile-menu">
            <div class="px-4 pt-2 pb-3 space-y-1 bg-white shadow-lg">
                <a href="../index.html#features"
                    class="block px-3 py-2 text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-lg transition-colors">Features</a>
                <a href="../index.html#pricing"
                    class="block px-3 py-2 text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-lg transition-colors">Pricing</a>
                <a href="index.html" class="block px-3 py-2 text-blue-600 bg-blue-50 rounded-lg font-medium">Contact</a>
                <a href="https://docforgeai.netlify.app/"
                    class="block w-full text-left px-3 py-2 text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-lg transition-colors">Sign
                    In</a>
                <a href="https://docforgeai.netlify.app/" class="btn-primary w-full mt-4 px-4 py-2 text-sm">Start Free
                    Trial</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section class="pt-32 pb-20 bg-mesh relative overflow-hidden">
            <div class="pattern-dots absolute inset-0 opacity-30"></div>

            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
                <div class="text-center max-w-4xl mx-auto">

                    <!-- Main Heading -->
                    <h1 class="text-5xl md:text-6xl lg:text-7xl font-black text-gray-900 mb-8 leading-tight">
                        Get in Touch with
                        <span class="text-gradient block mt-2">DocForge AI</span>
                    </h1>

                    <!-- Subheading -->
                    <p class="text-lg md:text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed">
                        Have questions? Need support? Want to learn more about our AI-powered document creation
                        platform?
                        <strong style="color: #10B981;" class="font-bold">We're here to help</strong> and respond within
                        hours.
                    </p>

                    <!-- CTA Buttons -->
                    <div class="flex flex-col sm:flex-row gap-6 justify-center mb-12">
                        <a href="#contact-form" class="btn-primary text-base px-4 py-2 group">
                            <svg class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                            Send Message
                        </a>
                        <a href="#contact-options" class="btn-secondary text-base px-4 py-2 group">
                            <svg class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" fill="currentColor"
                                viewBox="0 0 20 20">
                                <path
                                    d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                            </svg>
                            Call Us
                        </a>
                    </div>

                </div>
            </div>
        </section>

        <!-- Contact Form Section -->
        <section id="contact-form" class="py-20 bg-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl md:text-5xl font-black text-gray-900 mb-6">
                        Send Us a
                        <span class="text-gradient">Message</span>
                    </h2>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        Have a specific question or need personalized assistance? Fill out the form below and we'll get
                        back to you within 2 hours.
                    </p>
                </div>

                <div class="max-w-4xl mx-auto">
                    <div class="glass-card p-8 hover-lift">
                        <form class="space-y-6" id="contactForm" novalidate>
                            <!-- Name and Email Row -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="name" class="block text-sm font-semibold text-gray-900 mb-2">
                                        Full Name <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" id="name" name="name" required minlength="2" maxlength="50"
                                        autocomplete="name"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors bg-white/50 backdrop-blur-sm"
                                        placeholder="Enter your full name" aria-describedby="name-error">
                                    <div id="name-error" class="text-red-500 text-sm mt-1 hidden" role="alert"></div>
                                </div>

                                <div>
                                    <label for="email" class="block text-sm font-semibold text-gray-900 mb-2">
                                        Email Address <span class="text-red-500">*</span>
                                    </label>
                                    <input type="email" id="email" name="email" required autocomplete="email"
                                        inputmode="email"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors bg-white/50 backdrop-blur-sm"
                                        placeholder="Enter your email address" aria-describedby="email-error">
                                    <div id="email-error" class="text-red-500 text-sm mt-1 hidden" role="alert"></div>
                                </div>
                            </div>

                            <!-- Subject Dropdown -->
                            <div>
                                <label for="subject" class="block text-sm font-semibold text-gray-900 mb-2">
                                    Subject <span class="text-red-500">*</span>
                                </label>
                                <select id="subject" name="subject" required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors bg-white/50 backdrop-blur-sm"
                                    aria-describedby="subject-error">
                                    <option value="">Select a subject</option>
                                    <option value="general">General Inquiry</option>
                                    <option value="sales">Sales Question</option>
                                    <option value="support">Technical Support</option>
                                    <option value="partnership">Partnership Opportunity</option>
                                    <option value="feedback">Product Feedback</option>
                                    <option value="billing">Billing Question</option>
                                    <option value="other">Other</option>
                                </select>
                                <div id="subject-error" class="text-red-500 text-sm mt-1 hidden" role="alert"></div>
                            </div>

                            <!-- Inquiry Type Radio Buttons -->
                            <div>
                                <fieldset>
                                    <legend class="block text-sm font-semibold text-gray-900 mb-3">
                                        Inquiry Type <span class="text-red-500">*</span>
                                    </legend>
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <label
                                            class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                                            <input type="radio" name="inquiryType" value="sales" required
                                                class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                                                aria-describedby="inquiry-type-error">
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900">Sales</div>
                                                <div class="text-xs text-gray-500">Pricing, plans, demos</div>
                                            </div>
                                        </label>

                                        <label
                                            class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                                            <input type="radio" name="inquiryType" value="support" required
                                                class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500">
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900">Support</div>
                                                <div class="text-xs text-gray-500">Technical help, bugs</div>
                                            </div>
                                        </label>

                                        <label
                                            class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                                            <input type="radio" name="inquiryType" value="general" required
                                                class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500">
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900">General</div>
                                                <div class="text-xs text-gray-500">Other questions</div>
                                            </div>
                                        </label>
                                    </div>
                                    <div id="inquiry-type-error" class="text-red-500 text-sm mt-1 hidden" role="alert">
                                    </div>
                                </fieldset>
                            </div>

                            <!-- Message Textarea -->
                            <div>
                                <label for="message" class="block text-sm font-semibold text-gray-900 mb-2">
                                    Message <span class="text-red-500">*</span>
                                </label>
                                <textarea id="message" name="message" required minlength="10" maxlength="1000" rows="6"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors bg-white/50 backdrop-blur-sm resize-vertical"
                                    placeholder="Tell us how we can help you..."
                                    aria-describedby="message-error message-count"></textarea>
                                <div class="flex justify-between items-center mt-1">
                                    <div id="message-error" class="text-red-500 text-sm hidden" role="alert"></div>
                                    <div id="message-count" class="text-xs text-gray-500">0 / 1000 characters</div>
                                </div>
                            </div>

                            <!-- Form Actions -->
                            <div class="flex flex-col sm:flex-row gap-4 pt-6">
                                <button type="submit" class="btn-primary flex-1 text-base px-4 py-2 group"
                                    id="submitBtn">
                                    <svg class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" fill="none"
                                        stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                                    </svg>
                                    <span id="submitText">Send Message</span>
                                </button>
                            </div>

                            <!-- Success/Error Messages -->
                            <div id="form-success" class="hidden p-4 bg-green-50 border border-green-200 rounded-lg">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd"
                                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                            clip-rule="evenodd" />
                                    </svg>
                                    <div>
                                        <h4 class="text-green-800 font-semibold">Message sent successfully!</h4>
                                        <p class="text-green-700 text-sm">Thank you for contacting us. We'll get back to
                                            you within 2 hours.</p>
                                    </div>
                                </div>
                            </div>

                            <div id="form-error" class="hidden p-4 bg-red-50 border border-red-200 rounded-lg">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-red-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd"
                                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                                            clip-rule="evenodd" />
                                    </svg>
                                    <div>
                                        <h4 class="text-red-800 font-semibold">Please correct the errors below</h4>
                                        <p class="text-red-700 text-sm">Make sure all required fields are filled out
                                            correctly.</p>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </section>

        <!-- Company Information Section -->
        <section class="py-16 bg-gray-50 border-b border-gray-100">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-black text-gray-900 mb-4">
                        Visit Our
                        <span class="text-gradient">Office</span>
                    </h2>
                    <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                        We're located in the heart of Tech City. Drop by for a coffee or schedule a meeting with our
                        team.
                    </p>
                </div>

                <!-- Map and Social Media Section -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <!-- Map Placeholder -->
                    <div class="card hover-lift">
                        <div class="aspect-video bg-gray-200 rounded-lg flex items-center justify-center mb-4">
                            <div class="text-center">
                                <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m0 0L9 7" />
                                </svg>
                                <div class="text-gray-500 font-medium">Interactive Map</div>
                                <div class="text-sm text-gray-400">Coming Soon</div>
                            </div>
                        </div>
                        <h3 class="text-lg font-bold text-gray-900 mb-2">Find Us</h3>
                        <p class="text-gray-600 text-sm">
                            Located in the heart of San Francisco's SOMA district, our office is easily accessible by
                            BART, Muni, and major highways. Visitor parking available in the building garage.
                        </p>
                    </div>

                    <!-- Social Media and Additional Info -->
                    <div class="card hover-lift">
                        <h3 class="text-lg font-bold text-gray-900 mb-4">Connect With Us</h3>


                        <!-- Additional Contact Info -->
                        <div class="space-y-3 text-sm">
                            <div class="flex items-center text-gray-600">
                                <svg class="w-4 h-4 mr-3 text-blue-600" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                </svg>
                                <span>General: <EMAIL></span>
                            </div>
                            <div class="flex items-center text-gray-600">
                                <svg class="w-4 h-4 mr-3 text-green-600" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <span>Average response: 45 minutes</span>
                            </div>
                            <div class="flex items-center text-gray-600">
                                <svg class="w-4 h-4 mr-3 text-purple-600" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                </svg>
                                <span>Team size: 75+ AI experts</span>
                            </div>
                            <div class="flex items-center text-gray-600">
                                <svg class="w-4 h-4 mr-3 text-indigo-600" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <span>Founded in 2023</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white pt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Company Info -->
                <div class="md:col-span-2">
                    <div class="flex items-center mb-6">
                        <svg class="w-10 h-10 mr-3" viewBox="0 0 32 32" fill="currentColor" style="color: #1E3A8A;">
                            <path
                                d="M8 4h16a4 4 0 0 1 4 4v16a4 4 0 0 1-4 4H8a4 4 0 0 1-4-4V8a4 4 0 0 1 4-4zm2 6v12h12V10H10zm2 2h8v2h-8v-2zm0 4h8v2h-8v-2zm0 4h6v2h-6v-2z" />
                        </svg>
                        <span class="text-2xl font-bold">DocForge AI</span>
                    </div>
                    <p class="text-gray-300 mb-6 max-w-md">
                        Transform ideas into professional documents 10x faster with our AI-powered document creation
                        platform.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-white transition-all duration-200"
                            onmouseover="this.style.textDecoration='underline'"
                            onmouseout="this.style.textDecoration='none'">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path
                                    d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z" />
                            </svg>
                        </a>
                        <a href="#" class="text-white transition-all duration-200"
                            onmouseover="this.style.textDecoration='underline'"
                            onmouseout="this.style.textDecoration='none'">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path
                                    d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Product Links -->
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-white">Product</h3>
                    <ul class="space-y-2">
                        <li><a href="../index.html#features"
                                class="text-gray-300 hover:text-white transition-all duration-200"
                                onmouseover="this.style.textDecoration='underline'"
                                onmouseout="this.style.textDecoration='none'">Features</a></li>
                        <li><a href="../index.html#pricing"
                                class="text-gray-300 hover:text-white transition-all duration-200"
                                onmouseover="this.style.textDecoration='underline'"
                                onmouseout="this.style.textDecoration='none'">Pricing</a></li>
                        <li><a href="../index.html#testimonials"
                                class="text-gray-300 hover:text-white transition-all duration-200"
                                onmouseover="this.style.textDecoration='underline'"
                                onmouseout="this.style.textDecoration='none'">Testimonials</a></li>
                        <li><a href="https://docforgeai.netlify.app/"
                                class="text-gray-300 hover:text-white transition-all duration-200"
                                onmouseover="this.style.textDecoration='underline'"
                                onmouseout="this.style.textDecoration='none'">Try Free</a></li>
                    </ul>
                </div>

                <!-- Support Links -->
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-white">Support</h3>
                    <ul class="space-y-2">
                        <li><a href="index.html"
                                class="text-blue-400 hover:text-blue-300 transition-all duration-200 font-medium"
                                onmouseover="this.style.textDecoration='underline'"
                                onmouseout="this.style.textDecoration='none'">Contact Us</a></li>
                        <li><a href="../privacy-policy/index.html"
                                class="text-gray-300 hover:text-white transition-all duration-200"
                                onmouseover="this.style.textDecoration='underline'"
                                onmouseout="this.style.textDecoration='none'">Privacy Policy</a></li>
                        <li><a href="../terms-of-use/index.html"
                                class="text-gray-300 hover:text-white transition-all duration-200"
                                onmouseover="this.style.textDecoration='underline'"
                                onmouseout="this.style.textDecoration='none'">Terms of Use</a></li>
                    </ul>
                </div>
            </div>

            <!-- Copyright -->
            <div class="border-t border-gray-800 mt-12 py-6 text-center text-gray-300 text-sm">
                <p>&copy; 2025 DocForge AI. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="../js/main.js"></script>

</body>

</html>